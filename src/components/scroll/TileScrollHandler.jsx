import React, { useRef } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { useScroll } from '@react-three/drei';
import * as THREE from 'three';
import { TILE_POSITIONS } from '../projects/TestTiles';

/**
 * Tile Scroll Handler
 * Manages scroll-based camera panning to different tile positions
 */
const TileScrollHandler = ({ enabled = true }) => {
  const { camera } = useThree();
  const data = useScroll();
  
  const hasInitialized = useRef(false);
  
  // No animation state needed - we'll use continuous interpolation
  
  // Helper function to get camera position and orientation that faces the front of the tile
  const getCameraTransformForTile = (tilePosition, tileIndex) => {
    const tile = TILE_POSITIONS[tileIndex];
    const tileRotation = tile.rotation;
    
    // Create tile's rotation matrix to understand its orientation
    const tileMatrix = new THREE.Matrix4();
    const tileEuler = new THREE.Euler(tileRotation[0], tileRotation[1], tileRotation[2], 'XYZ');
    tileMatrix.makeRotationFromEuler(tileEuler);
    
    // Get the tile's front normal (negative Z direction after rotation)
    const frontNormal = new THREE.Vector3(0, 0, -1);
    frontNormal.applyMatrix4(tileMatrix);
    frontNormal.normalize();
    
    // Get the tile's up direction (Y direction after rotation)
    const upDirection = new THREE.Vector3(0, 1, 0);
    upDirection.applyMatrix4(tileMatrix);
    upDirection.normalize();
    
         // Position camera in front of the tile (slightly closer for better view of varied orientations)
     const viewingDistance = 15;
    const cameraPosition = new THREE.Vector3(...tilePosition)
      .add(frontNormal.clone().multiplyScalar(viewingDistance));
    
    // Create camera rotation that looks at tile center with proper up direction
    const cameraMatrix = new THREE.Matrix4();
    cameraMatrix.lookAt(
      cameraPosition,
      new THREE.Vector3(...tilePosition),
      upDirection
    );
    
    // Extract rotation from matrix
    const cameraRotation = new THREE.Euler();
    cameraRotation.setFromRotationMatrix(cameraMatrix);
    
    return {
      position: cameraPosition,
      rotation: cameraRotation
    };
  };
  
  // Easing function for smooth camera transitions
  const easeInOutCubic = (t) => {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  };

  useFrame(() => {
    if (!enabled || !data) return;
    
    const scrollProgress = data.offset;
    
    // Disable tile scrolling when in scroll-back ranges 
    // (very beginning or end of scroll)
    if (scrollProgress < 0.1 || scrollProgress > 0.85) {
      return; // Let ProjectsBackNavigation handle navigation back to home
    }
    
    // Initialize camera facing Tile 1
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      // Get Tile 1's camera transform and apply it immediately
      const tile1Transform = getCameraTransformForTile(TILE_POSITIONS[0].position, 0);
      camera.position.copy(tile1Transform.position);
      camera.rotation.copy(tile1Transform.rotation);
      console.log(`Initialized camera facing Tile 1`);
    }
    
    // Calculate continuous interpolation between tiles (Tile 1 to Tile 5)
    // Use scroll range from 10% to 85% for tile navigation (75% of total scroll)
    const tileScrollStart = 0.1;  // Reserve first 10% for scroll-back to home
    const tileScrollEnd = 0.85;   // Reserve last 15% for scroll-back to home
    const tileScrollRange = tileScrollEnd - tileScrollStart;
    const adjustedScrollProgress = (scrollProgress - tileScrollStart) / tileScrollRange;
    const clampedScrollProgress = Math.max(0, Math.min(adjustedScrollProgress, 1));
    const maxTileIndex = TILE_POSITIONS.length - 1; // 0 to 4 (5 tiles)
    const exactTileIndex = clampedScrollProgress * maxTileIndex;
    
    // Get the two tiles we're interpolating between
    const lowerTileIndex = Math.floor(exactTileIndex);
    const upperTileIndex = Math.min(lowerTileIndex + 1, maxTileIndex);
    const interpolationFactor = exactTileIndex - lowerTileIndex;
    
              // Get the tiles we're interpolating between
     const lowerTile = TILE_POSITIONS[lowerTileIndex];
     const upperTile = TILE_POSITIONS[upperTileIndex];
     
     // Get camera transforms for both tiles
     const lowerTransform = getCameraTransformForTile(lowerTile.position, lowerTileIndex);
     const upperTransform = getCameraTransformForTile(upperTile.position, upperTileIndex);
    
         // Smoothly interpolate camera position and rotation (slider effect)
     const smoothedFactor = easeInOutCubic(interpolationFactor);
     
     // Interpolate position
     const currentCameraPos = new THREE.Vector3().lerpVectors(
       lowerTransform.position,
       upperTransform.position,
       smoothedFactor
     );
     
     // Interpolate rotation using quaternions for smoother transitions
     const lowerQuaternion = new THREE.Quaternion().setFromEuler(lowerTransform.rotation);
     const upperQuaternion = new THREE.Quaternion().setFromEuler(upperTransform.rotation);
     const currentQuaternion = new THREE.Quaternion().slerpQuaternions(lowerQuaternion, upperQuaternion, smoothedFactor);
     const currentCameraRotation = new THREE.Euler().setFromQuaternion(currentQuaternion);
     
     // Validate camera position to prevent glitches
     if (currentCameraPos.length() > 1000 || isNaN(currentCameraPos.x) || isNaN(currentCameraPos.y) || isNaN(currentCameraPos.z)) {
       console.warn('Invalid camera position detected, skipping update:', currentCameraPos);
       return;
     }
     
     // Apply position and rotation to camera
     camera.position.copy(currentCameraPos);
     camera.rotation.copy(currentCameraRotation);
    
              // Camera rotation is now handled above, no need for lookAt
    
         // Debug logging with camera position info
     if (Math.floor(exactTileIndex * 10) !== Math.floor((exactTileIndex - 0.1) * 10)) {
       console.log(`Sliding between Tile ${lowerTileIndex + 1} and Tile ${upperTileIndex + 1}, progress: ${interpolationFactor.toFixed(2)}`);
       console.log(`Camera position:`, currentCameraPos.toArray());
       console.log(`Camera rotation:`, [currentCameraRotation.x, currentCameraRotation.y, currentCameraRotation.z]);
     }
  });

  return null;
};

export default TileScrollHandler; 