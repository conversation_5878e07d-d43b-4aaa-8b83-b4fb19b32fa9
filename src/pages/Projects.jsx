import React, { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { PerspectiveCamera, ScrollControls } from '@react-three/drei';
import * as THREE from 'three';
import ProjectsBackNavigation from '../components/scroll/ProjectsBackNavigation';
import ProjectsScene from '../components/projects/ProjectsScene';
import {
    BACKGROUND_COLOR,
    FOG_COLOR,
    FOG_NEAR,
    FOG_FAR,
    CAMERA_FOV,
    CAMERA_NEAR,
    CAMERA_FAR,
} from '../constants/simpleParticleConstants';

const Projects = () => {
    return (
        <div className="relative w-full h-screen bg-black overflow-hidden">
            {/* Main 3D canvas with improved settings */}
            <Canvas
                shadows
                gl={{ 
                    antialias: true,
                    alpha: false,
                    powerPreference: "high-performance"
                }}
                onCreated={({ gl }) => {
                    gl.setClearColor(new THREE.Color(BACKGROUND_COLOR));
                    gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                }}
            >
                <fog attach="fog" args={[FOG_COLOR, FOG_NEAR, FOG_FAR]} />

                <PerspectiveCamera
                    makeDefault
                    fov={CAMERA_FOV}
                    near={CAMERA_NEAR}
                    far={CAMERA_FAR}
                    // Initial position is now primarily managed by CameraController's useEffect
                />

                <Suspense fallback={null}>
                    <ScrollControls pages={5} damping={0.25} scroll={0.1}>
                        <ProjectsScene />
                        <ProjectsBackNavigation />
                    </ScrollControls>
                </Suspense>
            </Canvas>

            {/* UI Overlay */}
            <div className="absolute top-4 right-4 text-white text-xs bg-black bg-opacity-50 p-2 rounded backdrop-blur-sm">
                <span className="hidden sm:inline">Tile Slider Galaxy | </span>
                <span>Scroll to slide between tiles</span>
            </div>

            {/* Navigation hint */}
            <div className="absolute bottom-4 left-4 text-white text-xs bg-black bg-opacity-50 p-2 rounded backdrop-blur-sm">
                <span className="opacity-75">Scroll down through tiles • Scroll up at top/end to return home</span>
            </div>
        </div>
    );
};

export default Projects;