{"name": "portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-spring/three": "^10.0.2", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "framer-motion": "^12.6.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.1", "three": "^0.175.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/postcss": "^4.1.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "vite": "^6.2.0"}}