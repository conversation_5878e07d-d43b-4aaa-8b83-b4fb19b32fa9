# Portfolio Website Architecture

## Overview
This portfolio website is built with React, Three.js (via React Three Fiber), and modern web technologies. The architecture follows clean coding principles with reusable components, centralized state management, and modular design.

## Project Structure

### Core Pages
- **Home** (`/src/pages/Home.jsx`) - Landing page with particle text animation
- **Projects** (`/src/pages/Projects.jsx`) - Galaxy view with floating project tiles

### Reusable Components

#### Particles
- **GalaxyParticleSystem** (`/src/components/particles/`) - Reusable particle system for creating starfield effects
- Supports customizable particle count, colors, size, and animation

#### Camera
- **CameraController** (`/src/components/camera/`) - Handles mouse interaction, dragging, and smooth camera movement
- Configurable mouse sensitivity, zoom limits, and easing parameters

#### Projects
- **ProjectTile** (`/src/components/projects/ProjectTile.jsx`) - Individual floating project cards
- **ProjectsScene** (`/src/components/projects/ProjectsScene.jsx`) - Main scene orchestrator
- **ProjectGalaxy** (`/src/components/projects/ProjectGalaxy.jsx`) - Galaxy wrapper with future expansion capabilities

#### Scroll Management
- **HomeScrollHandler** (`/src/components/scroll/`) - Manages Home to Projects navigation
- **ProjectsScrollHandler** (`/src/components/scroll/`) - Manages Projects to Home navigation
- **useScrollNavigation** (`/src/hooks/`) - Custom hook for scroll-based navigation

### Utilities
- **cameraUtils** (`/src/utils/cameraUtils.js`) - Camera calculation utilities
- **particleUtils** (`/src/utils/particleUtils.js`) - Particle system utilities

### Constants
- **simpleParticleConstants** - Main scene configuration
- **projectsConstants** - Project-specific configuration
- **particleTextConstants** - Home page particle text settings

## Design Principles

### 1. Modularity
- Each component has a single responsibility
- Reusable components can be configured via props
- Clear separation between UI, logic, and data

### 2. Performance
- Throttled scroll updates (60fps)
- Optimized particle rendering
- Efficient memory usage with refs and cleanup

### 3. Maintainability
- Well-documented code with JSDoc comments
- Consistent naming conventions
- Centralized configuration in constants

### 4. Scalability
- Easy to add new projects via configuration
- Extensible component architecture
- Future-ready project management system

## Key Features

### Galaxy Navigation
- Smooth scroll-based transitions between pages
- Mouse drag controls for camera movement
- Particle backgrounds for immersive experience

### Project Display
- Floating project tiles in 3D space
- Interactive hover effects and animations
- Prepared for future search and filtering

### Responsive Design
- Mobile-optimized controls and layouts
- Adaptive text and UI elements
- Performance optimizations for all devices

## Future Enhancements

The architecture supports easy addition of:
- Individual project detail pages
- Search and filtering functionality
- Project categories and tags
- Admin interface for project management
- Additional particle effects and animations

## Development Guidelines

### Adding New Projects
1. Update `PORTFOLIO_ITEMS` in `simpleParticleConstants.js`
2. Add project assets to `/public/portfolio/`
3. Configure position and metadata

### Creating New Components
1. Follow the established folder structure
2. Use TypeScript-style prop definitions
3. Include comprehensive JSDoc documentation
4. Extract reusable logic to utility functions

### Performance Considerations
- Use `useFrame` sparingly and efficiently
- Implement proper cleanup in `useEffect`
- Throttle expensive calculations
- Optimize asset loading and caching 