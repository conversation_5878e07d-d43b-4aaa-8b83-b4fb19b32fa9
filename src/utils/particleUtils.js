import * as THREE from 'three';

/**
 * Particle System Utility Functions
 * Reusable functions for particle generation and manipulation
 */

/**
 * Generate random position within a sphere
 */
export const generateSpherePosition = (radius) => {
  const theta = Math.random() * Math.PI * 2;
  const phi = Math.acos(2 * Math.random() - 1);
  const r = Math.random() * radius;

  return {
    x: r * Math.sin(phi) * Math.cos(theta),
    y: r * Math.sin(phi) * Math.sin(theta),
    z: r * Math.cos(phi)
  };
};

/**
 * Generate random color between two colors
 */
export const generateMixedColor = (color1, color2) => {
  const c1 = new THREE.Color(color1);
  const c2 = new THREE.Color(color2);
  const mixFactor = Math.random();
  return c1.clone().lerp(c2, mixFactor);
};

/**
 * Initialize particle positions array with spherical distribution
 */
export const initializeParticlePositions = (count, radius) => {
  const positions = new Float32Array(count * 3);
  
  for (let i = 0; i < count; i++) {
    const pos = generateSpherePosition(radius);
    positions[i * 3] = pos.x;
    positions[i * 3 + 1] = pos.y;
    positions[i * 3 + 2] = pos.z;
  }
  
  return positions;
};

/**
 * Initialize particle colors array with color mixing
 */
export const initializeParticleColors = (count, color1, color2) => {
  const colors = new Float32Array(count * 3);
  
  for (let i = 0; i < count; i++) {
    const mixedColor = generateMixedColor(color1, color2);
    colors[i * 3] = mixedColor.r;
    colors[i * 3 + 1] = mixedColor.g;
    colors[i * 3 + 2] = mixedColor.b;
  }
  
  return colors;
};

/**
 * Create particle material with optional texture
 */
export const createParticleMaterial = ({
  size = 0.1,
  opacity = 0.8,
  texture = null,
  vertexColors = true,
  blending = THREE.AdditiveBlending
}) => {
  const materialProps = {
    size,
    sizeAttenuation: true,
    depthWrite: false,
    vertexColors,
    blending,
    transparent: true,
    opacity,
    alphaTest: 0.1
  };

  if (texture) {
    materialProps.map = texture;
  }

  return materialProps;
};

/**
 * Update particle animation (rotation, floating, etc.)
 */
export const updateParticleAnimation = (particleRef, time, animationSpeed = 0.001) => {
  if (particleRef.current) {
    particleRef.current.rotation.y = time * animationSpeed;
  }
};

export default {
  generateSpherePosition,
  generateMixedColor,
  initializeParticlePositions,
  initializeParticleColors,
  createParticleMaterial,
  updateParticleAnimation
}; 