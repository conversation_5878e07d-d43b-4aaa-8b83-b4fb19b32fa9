import React, { useState, useEffect } from 'react';
import { Suspense } from 'react';
import ProjectsScene from './ProjectsScene';
import { PORTFOLIO_ITEMS } from '../../constants/simpleParticleConstants';

/**
 * Project Galaxy Wrapper Component
 * Manages the overall project galaxy experience and future enhancements
 */
const ProjectGalaxy = () => {
  const [projects, setProjects] = useState(PORTFOLIO_ITEMS);
  const [filteredProjects, setFilteredProjects] = useState(PORTFOLIO_ITEMS);
  const [selectedTags, setSelectedTags] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter projects based on search and tags (for future implementation)
  useEffect(() => {
    let filtered = projects;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(project =>
        project.tags && selectedTags.some(tag => project.tags.includes(tag))
      );
    }

    setFilteredProjects(filtered);
  }, [projects, searchQuery, selectedTags]);

  // Future methods for project management (placeholder for future use)
  const _addProject = (projectData) => {
    const newProject = {
      ...projectData,
      id: Math.max(...projects.map(p => p.id)) + 1
    };
    setProjects([...projects, newProject]);
  };

  const _updateProject = (id, updates) => {
    setProjects(projects.map(project =>
      project.id === id ? { ...project, ...updates } : project
    ));
  };

  const _removeProject = (id) => {
    setProjects(projects.filter(project => project.id !== id));
  };

  const _handleSearch = (query) => {
    setSearchQuery(query);
  };

  const _handleTagFilter = (tags) => {
    setSelectedTags(tags);
  };

  return (
    <Suspense fallback={null}>
      {/* Main scene with filtered projects */}
      <ProjectsScene 
        projects={filteredProjects}
      />
      
      {/* Future UI components can be added here */}
      {/* Search bar, tag filters, project creation modal, etc. */}
    </Suspense>
  );
};

export default ProjectGalaxy; 