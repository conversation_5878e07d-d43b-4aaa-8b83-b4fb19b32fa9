import React, { useRef } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useScroll } from '@react-three/drei';
import * as THREE from 'three';
import { TILE_POSITIONS } from '../../constants/projectsConstants';

const UnifiedScrollHandler = ({ particleTextRef, onSceneChange }) => {
  const data = useScroll();
  const { camera } = useThree();
  const prevScrollRef = useRef(0);
  const currentSceneRef = useRef('home');
  const transitioningRef = useRef(false);

  useFrame(() => {
    if (!data) return;
    const scroll = data.offset; // 0..1 across 6 pages

    // Zones
    const homeZoneEnd = 1 / 6; // ~0.166
    const tilesZoneEnd = 5 / 6; // reserve last page for upward nav comfort

    // 1) Home zoom / animation control via ParticleText API if present
    if (particleTextRef?.current) {
      // Map 0..homeZoneEnd -> 0..1 for the zoom-in/out API
      const homeProgress = THREE.MathUtils.clamp(scroll / homeZoneEnd, 0, 1);
      particleTextRef.current.updateZoom(homeProgress);
    }

    // 2) Scene switching logic with better transition detection
    // Allow a small overlap zone for smoother transitions
    const transitionBuffer = 0.03; // 3% buffer zone
    
    if (scroll <= homeZoneEnd + transitionBuffer) {
      // In home zone or transition buffer
      if (currentSceneRef.current !== 'home') {
        transitioningRef.current = true;
        currentSceneRef.current = 'home';
        if (onSceneChange) onSceneChange('home');
        console.log('Switched to HOME scene at scroll:', scroll.toFixed(3));
        
        // Force a small delay to ensure scene has switched before positioning camera
        setTimeout(() => {
          transitioningRef.current = false;
        }, 50);
      }
      
      // Set appropriate camera position for home scene
      // Map current scroll position to home camera state
      const homeProgress = THREE.MathUtils.clamp(scroll / homeZoneEnd, 0, 1);
      
      // Home camera positioning - match ParticleSystem's camera logic exactly
      // ParticleSystem uses CAMERA_POSITION [0,0,100] to CAMERA_END_POSITION [2,25,10]
      const startPos = new THREE.Vector3(0, 0, 100);
      const endPos = new THREE.Vector3(2, 25, 10);
      
      // Use the same easing function as ParticleSystem
      const easeInOutCubic = (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
      const easedProgress = easeInOutCubic(homeProgress);
      
      // Interpolate camera position exactly like ParticleSystem does
      const newPosition = new THREE.Vector3().lerpVectors(startPos, endPos, easedProgress);
      
      camera.position.copy(newPosition);
      camera.lookAt(0, 0, 0);
      camera.rotation.set(0, 0, 0); // Reset any rotation from projects scene
      
      console.log(`Home camera: scroll=${scroll.toFixed(3)}, progress=${homeProgress.toFixed(3)}, pos=[${newPosition.x.toFixed(1)},${newPosition.y.toFixed(1)},${newPosition.z.toFixed(1)}]`);
      
      prevScrollRef.current = scroll;
      return;
    } else {
      // In projects zone
      if (currentSceneRef.current !== 'projects') {
        currentSceneRef.current = 'projects';
        if (onSceneChange) onSceneChange('projects');
        console.log('Switched to PROJECTS scene at scroll:', scroll.toFixed(3));
      }
      
      // Handle projects scene camera control
      handleProjectsCamera(scroll, homeZoneEnd, transitionBuffer, tilesZoneEnd);
    }

    // Store current scroll for next frame
    prevScrollRef.current = scroll;
  });

  // Projects camera control function
  const handleProjectsCamera = (scroll, homeZoneEnd, transitionBuffer, tilesZoneEnd) => {
    // Compute tile sliding progress within tiles zone (accounting for buffer)
    const effectiveTilesStart = homeZoneEnd + transitionBuffer;
    const tilesRange = tilesZoneEnd - effectiveTilesStart;
    const tilesProgress = THREE.MathUtils.clamp((scroll - effectiveTilesStart) / tilesRange, 0, 1);

    // Map 0..1 to tile indices 0..4
    const exactTileIndex = tilesProgress * (TILE_POSITIONS.length - 1);
    const lowerIndex = Math.floor(exactTileIndex);
    const upperIndex = Math.min(lowerIndex + 1, TILE_POSITIONS.length - 1);
    const t = THREE.MathUtils.smoothstep(exactTileIndex - lowerIndex, 0, 1);

    // Complete tile camera transform function (from TileScrollHandler)
    const getCameraTransformForTile = (tilePosition, tileIndex) => {
      const tile = TILE_POSITIONS[tileIndex];
      const tileRotation = tile.rotation;
      
      // Create tile's rotation matrix to understand its orientation
      const tileMatrix = new THREE.Matrix4();
      const tileEuler = new THREE.Euler(tileRotation[0], tileRotation[1], tileRotation[2], 'XYZ');
      tileMatrix.makeRotationFromEuler(tileEuler);
      
      // Get the tile's front normal (negative Z direction after rotation)
      const frontNormal = new THREE.Vector3(0, 0, -1);
      frontNormal.applyMatrix4(tileMatrix);
      frontNormal.normalize();
      
      // Get the tile's up direction (Y direction after rotation)
      const upDirection = new THREE.Vector3(0, 1, 0);
      upDirection.applyMatrix4(tileMatrix);
      upDirection.normalize();
      
      // Position camera in front of the tile
      const viewingDistance = 15;
      const cameraPosition = new THREE.Vector3(...tilePosition)
        .add(frontNormal.clone().multiplyScalar(viewingDistance));
      
      // Create camera rotation that looks at tile center with proper up direction
      const cameraMatrix = new THREE.Matrix4();
      cameraMatrix.lookAt(
        cameraPosition,
        new THREE.Vector3(...tilePosition),
        upDirection
      );
      
      // Extract rotation from matrix
      const cameraRotation = new THREE.Euler();
      cameraRotation.setFromRotationMatrix(cameraMatrix);
      
      return {
        position: cameraPosition,
        rotation: cameraRotation
      };
    };

    const lowerTransform = getCameraTransformForTile(TILE_POSITIONS[lowerIndex].position, lowerIndex);
    const upperTransform = getCameraTransformForTile(TILE_POSITIONS[upperIndex].position, upperIndex);

    // Smooth interpolation using quaternions for rotation
    const easeInOutCubic = (t) => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    const smoothedFactor = easeInOutCubic(t);
    
    // Interpolate position
    const currentCameraPos = new THREE.Vector3().lerpVectors(
      lowerTransform.position,
      upperTransform.position,
      smoothedFactor
    );
    
    // Interpolate rotation using quaternions for smooth transitions
    const lowerQuaternion = new THREE.Quaternion().setFromEuler(lowerTransform.rotation);
    const upperQuaternion = new THREE.Quaternion().setFromEuler(upperTransform.rotation);
    const currentQuaternion = new THREE.Quaternion().slerpQuaternions(lowerQuaternion, upperQuaternion, smoothedFactor);

    // Apply to camera
    camera.position.copy(currentCameraPos);
    camera.quaternion.copy(currentQuaternion);
  };

  return null;
};

export default UnifiedScrollHandler; 