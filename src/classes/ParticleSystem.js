import {
    DEFAULT_TEXT,
    <PERSON><PERSON><PERSON><PERSON>_TEXT,
    <PERSON><PERSON><PERSON><PERSON>_BREAKPOINT,
    PART<PERSON>LE_AMOUNT,
    PART<PERSON><PERSON>_SIZE,
    PART<PERSON><PERSON>_COLOR,
    INTERACTION_AREA,
    DEFAULT_EASE,
    PRESSED_EASE,
    RELEASED_EASE,
    DEFAULT_IMAGE_URL,
    MIN_TEXT_SIZE,
    DESKTOP_TEXT_Y_POSITION,
    MO<PERSON>LE_TEXT_Y_POSITION,
    IMAGE_ANIMATION_DURATION,
    IMAGE_START_Y,
    MO<PERSON>LE_IMAGE_TARGET_Y,
    DESKTOP_IMAGE_TARGET_Y,
    IMAGE_Z_POSITION,
    MOUSE_MOVE_FACTOR,
    MOUSE_ROTATION_FACTOR,
    ROTATION_EASE,
    PARTICLE_DISTANCE_THRESHOLD,
    MATERIAL_SETTINGS,
    AMBIENT_LIGHT_INTENSITY,
    DIRECTIONAL_LIGHT_INTENSITY,
    DIREC<PERSON>ONAL_LIGHT_POSITION,
    CAMERA_FOV,
    <PERSON><PERSON>RA_POSITION,
    CAMERA_NEAR,
    CAMERA_FAR,
    RESI<PERSON><PERSON>_DEBOUNCE_TIME,
    DESKTOP_IMAGE_SIZE,
    MOBILE_IMAGE_SIZE,
    FONT_RETRY_DELAY
} from "../constants/particleTextConstants";
import { ZOOM_DURATION, CAMERA_END_POSITION } from "../constants/transitionConstants";
import * as THREE from "three";


export class ParticleSystem {
    constructor(
      scene,
      font,
      particleImg,
      imageTexture,
      camera,
      renderer,
      options
    ) {
      this.scene = scene;
      this.font = font;
      this.particleImg = particleImg;
      this.imageTexture = imageTexture;
      this.camera = camera;
      this.renderer = renderer;
      this.options = options;

      // Check if we're in mobile view (from options)
      this.isMobileView = this.options.text.includes('\n');

      // Set text position based on view
      this.textYPosition = this.isMobileView ? MOBILE_TEXT_Y_POSITION : DESKTOP_TEXT_Y_POSITION;
      this.imageSize = this.isMobileView ? MOBILE_IMAGE_SIZE : DESKTOP_IMAGE_SIZE;
      this.raycaster = new THREE.Raycaster();
      this.mouse = new THREE.Vector2(-200, 200);
      this.colorChange = new THREE.Color(1, 1, 1); // Initialize with white color
      this.buttonDown = false;
      this.currentPosition = null;

      this.init();
    }

    init() {
      try {
        // Create invisible plane for interaction
        const geometry = new THREE.PlaneGeometry(
          this.visibleWidthAtZDepth(100, this.camera),
          this.visibleHeightAtZDepth(100, this.camera)
        );
        const material = new THREE.MeshBasicMaterial({
          color: 0x00ff00,
          transparent: true,
        });
        this.planeArea = new THREE.Mesh(geometry, material);
        this.planeArea.visible = false;
        this.scene.add(this.planeArea);

        // Check if font is loaded before creating text
        if (this.font) {
          this.createText();
        } else {
          console.warn("Font not loaded yet, waiting...");
          // Try again after a delay
          setTimeout(() => {
            if (this.font) {
              console.log("Font loaded after delay, creating text now");
              this.createText();
            } else {
              console.error("Font still not loaded after delay");
            }
          }, FONT_RETRY_DELAY);
        }

        this.createImage();
        this.setupEventListeners();
      } catch (error) {
        console.error("Error in init:", error);
      }
    }

    setupEventListeners() {
      // Track interaction state
      this.isInteracting = false;
      this.lastTouchY = 0;
      this.touchStartTime = 0;
      this.isTouchScrolling = false;
      this.isTouchDevice = false; // Flag to track if we're on a touch device
      this.longPressTimer = null; // Timer for detecting long press
      this.longPressThreshold = 800; // Time in ms to consider a touch as a long press
      this.hasLongPressed = false; // Flag to track if long press has been triggered

      const handleMouseDown = (e) => {
        if (this.isTouchDevice) return; // Skip if touch device already interacted
        this.buttonDown = true;
        this.options.ease = PRESSED_EASE;
        this.updateMousePosition(e);
      };

      const handleMouseUp = () => {
        if (this.isTouchDevice) return; // Skip if touch device already interacted
        this.buttonDown = false;
        this.options.ease = RELEASED_EASE;
      };

      const handleMouseMove = (e) => {
        if (this.isTouchDevice) return; // Skip if touch device already interacted
        this.updateMousePosition(e);
      };

      // Touch event handlers for mobile devices
      const handleTouchStart = (e) => {
        // Mark as touch device on first touch
        this.isTouchDevice = true;

        // Store initial touch position for determining scroll vs. interaction
        if (e.touches.length === 1) {
          this.lastTouchY = e.touches[0].clientY;
          this.touchStartTime = Date.now();
          this.isInteracting = true;
          this.isTouchScrolling = false;
          this.hasLongPressed = false;

          // Update touch position for rotation effects
          this.updateTouchPosition(e);

          // Set up long press timer
          clearTimeout(this.longPressTimer);
          this.longPressTimer = setTimeout(() => {
            // Only trigger long press if we haven't scrolled
            if (this.isInteracting && !this.isTouchScrolling) {
              console.log('Long press detected!');
              this.buttonDown = true; // Enable dissolve effect
              this.options.ease = PRESSED_EASE;
              this.hasLongPressed = true;
            }
          }, this.longPressThreshold);
        }
      };

      const handleTouchEnd = () => {
        clearTimeout(this.longPressTimer);
        this.isInteracting = false;
        this.isTouchScrolling = false;

        // Always reset buttonDown on touch end to ensure particles return to original positions
        this.buttonDown = false;
        this.hasLongPressed = false;

        // Set a faster ease value to make particles return to position more quickly after touch release
        // Use a faster ease value for touch release after long press
        this.options.ease = this.hasLongPressed ? RELEASED_EASE * 1.5 : RELEASED_EASE;
      };

      const handleTouchMove = (e) => {
        if (!this.isInteracting || e.touches.length !== 1) return;

        const touchY = e.touches[0].clientY;
        const deltaY = touchY - this.lastTouchY;
        const timeDelta = Date.now() - this.touchStartTime;

        // If significant movement, cancel long press timer
        if (Math.abs(deltaY) > 10) {
          clearTimeout(this.longPressTimer);
        }

        // If vertical movement is significant and it's been less than 300ms,
        if (Math.abs(deltaY) > 15 && timeDelta < 300) {
          this.isTouchScrolling = true;
          this.updateTouchPosition(e);
        }

        // If we've determined this is not a scroll, treat it as an interaction
        if (!this.isTouchScrolling) {
          // Only prevent default for horizontal movements or after determining it's not a scroll
          if (Math.abs(deltaY) < 10 || timeDelta >= 300) {
            e.preventDefault();
          }

          this.updateTouchPosition(e);
        }

        this.lastTouchY = touchY;
      };

      // Add mouse event listeners
      document.addEventListener("mousedown", handleMouseDown);
      document.addEventListener("mouseup", handleMouseUp);
      document.addEventListener("mousemove", handleMouseMove);

      // Add touch event listeners
      document.addEventListener("touchstart", handleTouchStart, { passive: true });
      document.addEventListener("touchend", handleTouchEnd);
      document.addEventListener("touchmove", handleTouchMove, { passive: false });

      // Cleanup
      this.cleanup = () => {
        document.removeEventListener("mousedown", handleMouseDown);
        document.removeEventListener("mouseup", handleMouseUp);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("touchstart", handleTouchStart);
        document.removeEventListener("touchend", handleTouchEnd);
        document.removeEventListener("touchmove", handleTouchMove);
      };
    }

    updateMousePosition(e) {
      this.mouse.x = (e.clientX / window.innerWidth) * 2 - 1;
      this.mouse.y = -(e.clientY / window.innerHeight) * 2 + 1;

      const vector = new THREE.Vector3(this.mouse.x, this.mouse.y, 0.5);
      vector.unproject(this.camera);
      const dir = vector.sub(this.camera.position).normalize();
      const distance = -this.camera.position.z / dir.z;
      this.currentPosition = this.camera.position
        .clone()
        .add(dir.multiplyScalar(distance));
    }

    updateTouchPosition(e) {
      // Get the first touch point
      if (e.touches.length > 0) {
        const touch = e.touches[0];

        // Convert touch coordinates to normalized device coordinates (-1 to +1)
        this.mouse.x = (touch.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(touch.clientY / window.innerHeight) * 2 + 1;

        // Calculate 3D position from touch coordinates
        const vector = new THREE.Vector3(this.mouse.x, this.mouse.y, 0.5);
        vector.unproject(this.camera);
        const dir = vector.sub(this.camera.position).normalize();
        const distance = -this.camera.position.z / dir.z;
        this.currentPosition = this.camera.position
          .clone()
          .add(dir.multiplyScalar(distance));

        // For debugging
        // console.log(`Touch at (${touch.clientX}, ${touch.clientY}) -> 3D: (${this.currentPosition.x.toFixed(2)}, ${this.currentPosition.y.toFixed(2)}, ${this.currentPosition.z.toFixed(2)})`);
      }
    }

    createImage() {
      if (!this.imageTexture) return;

      // Calculate the aspect ratio of the image
      const imageAspect =
        this.imageTexture.image.width / this.imageTexture.image.height;
      const width = this.imageSize * imageAspect;
      const height = this.imageSize;

      // Create a plane geometry for the image
      const geometry = new THREE.PlaneGeometry(width, height);

      // Enhanced material settings
      const material = new THREE.MeshStandardMaterial({
        map: this.imageTexture,
        transparent: true,
        roughness: MATERIAL_SETTINGS.ROUGHNESS,
        metalness: MATERIAL_SETTINGS.METALNESS,
        color: MATERIAL_SETTINGS.COLOR,
        emissive: MATERIAL_SETTINGS.EMISSIVE,
        emissiveIntensity: MATERIAL_SETTINGS.EMISSIVE_INTENSITY,
        side: THREE.DoubleSide,
      });

      // Ensure proper texture settings
      this.imageTexture.encoding = THREE.sRGBEncoding;
      this.imageTexture.anisotropy = 16;
      this.imageTexture.needsUpdate = true;

      // Create the mesh
      this.imageMesh = new THREE.Mesh(geometry, material);
      this.imageMesh.position.set(0, 0, IMAGE_Z_POSITION);

      // Add subtle ambient light to help with visibility
      if (!this.ambientLight) {
        this.ambientLight = new THREE.AmbientLight(0xffffff, AMBIENT_LIGHT_INTENSITY);
        this.scene.add(this.ambientLight);
      }

      // Add directional light for better contrast
      if (!this.directionalLight) {
        this.directionalLight = new THREE.DirectionalLight(0xffffff, DIRECTIONAL_LIGHT_INTENSITY);
        this.directionalLight.position.set(...DIRECTIONAL_LIGHT_POSITION);
        this.scene.add(this.directionalLight);
      }

      this.scene.add(this.imageMesh);

      // Animation setup
      this.imageMesh.position.y = IMAGE_START_Y;
      this.imageAnimation = {
        startTime: performance.now(),
        duration: IMAGE_ANIMATION_DURATION,
        startY: IMAGE_START_Y,
        targetY: this.isMobileView ? MOBILE_IMAGE_TARGET_Y : DESKTOP_IMAGE_TARGET_Y,
      };
    }

    createText() {
      if (!this.font) {
        console.error("Font not loaded properly");
        return;
      }

      const thePoints = [];
      const colors = [];
      const sizes = [];

      try {
        // Generate shapes from text
        const shapes = this.font.generateShapes(
          this.options.text,
          this.options.textSize
        );
        const geometry = new THREE.ShapeGeometry(shapes);
        geometry.computeBoundingBox();

        // Center the text
        const xMid =
          -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
        const yMid =
          (geometry.boundingBox.max.y - geometry.boundingBox.min.y) / 2.85;
        geometry.center();

        const holeShapes = [];
        for (let q = 0; q < shapes.length; q++) {
          const shape = shapes[q];
          if (shape.holes && shape.holes.length > 0) {
            holeShapes.push(...shape.holes);
          }
        }
        shapes.push(...holeShapes);

        // Create points for each shape
        for (let x = 0; x < shapes.length; x++) {
          const shape = shapes[x];
          const amountPoints =
            shape.type === "Path" ? this.options.amount / 2 : this.options.amount;
          const points = shape.getSpacedPoints(amountPoints);

          points.forEach((element) => {
            thePoints.push(new THREE.Vector3(element.x, element.y, 0));
            colors.push(
              this.colorChange.r,
              this.colorChange.g,
              this.colorChange.b
            );
            sizes.push(1);
          });
        }

        // Create particle geometry
        const geoParticles = new THREE.BufferGeometry().setFromPoints(thePoints);
        geoParticles.translate(xMid, yMid + this.textYPosition, 0); // Apply text Y position offset
        geoParticles.setAttribute(
          "customColor",
          new THREE.Float32BufferAttribute(colors, 3)
        );
        geoParticles.setAttribute(
          "size",
          new THREE.Float32BufferAttribute(sizes, 1)
        );

        // Create particle material
        const material = new THREE.ShaderMaterial({
          uniforms: {
            color: { value: new THREE.Color(this.options.particleColor) },
            pointTexture: { value: this.particleImg },
          },
          vertexShader: `
          attribute float size;
          attribute vec3 customColor;
          varying vec3 vColor;

          void main() {
            vColor = customColor;
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * (300.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
          }
        `,
          fragmentShader: `
          uniform vec3 color;
          uniform sampler2D pointTexture;
          varying vec3 vColor;

          void main() {
            gl_FragColor = vec4(color * vColor, 1.0);
            gl_FragColor = gl_FragColor * texture2D(pointTexture, gl_PointCoord);
          }
        `,
          blending: THREE.NormalBlending,
          depthTest: false,
          transparent: true,
        });

        // Create particle system
        this.particles = new THREE.Points(geoParticles, material);
        this.scene.add(this.particles);

        // Store original positions
        this.geometryCopy = new THREE.BufferGeometry();
        this.geometryCopy.copy(this.particles.geometry);
      } catch (error) {
        console.error("Error creating text:", error);
      }
    }

    render() {
      this.raycaster.setFromCamera(this.mouse, this.camera);
      const intersects = this.raycaster.intersectObject(this.planeArea);

      // Handle image animation and interaction
      if (this.imageMesh) {
        // Handle slide-up animation
        if (this.imageAnimation) {
          const now = performance.now();
          const elapsed = now - this.imageAnimation.startTime;

          if (elapsed < this.imageAnimation.duration) {
            // Calculate animation progress (0 to 1)
            const progress = elapsed / this.imageAnimation.duration;

            // Use easeOutCubic easing function for smooth animation
            const easeOut = 1 - Math.pow(1 - progress, 3);

            // Update position based on animation progress
            const newY =
              this.imageAnimation.startY +
              (this.imageAnimation.targetY - this.imageAnimation.startY) *
                easeOut;
            this.imageMesh.position.y = newY;
          } else {
            // Animation complete
            this.imageMesh.position.y = this.imageAnimation.targetY;
            this.imageAnimation = null; // Clear animation data
          }
        }

        // Apply rotation when mouse/touch moves and after initial animation is complete
        if (this.currentPosition && !this.imageAnimation) {
          // Continue rotation even during scrolling
          // Calculate the target rotation based on mouse/touch position
          const targetRotationY = this.mouse.x * MOUSE_ROTATION_FACTOR;
          const targetRotationX = -this.mouse.y * MOUSE_ROTATION_FACTOR;

          // Smoothly interpolate current rotation to target rotation
          // Use a slower ease factor during scrolling for smoother effect
          const easeFactorY = this.isTouchScrolling ? ROTATION_EASE * 0.7 : ROTATION_EASE;
          const easeFactorX = this.isTouchScrolling ? ROTATION_EASE * 0.7 : ROTATION_EASE;

          this.imageMesh.rotation.y +=
            (targetRotationY - this.imageMesh.rotation.y) * easeFactorY;
          this.imageMesh.rotation.x +=
            (targetRotationX - this.imageMesh.rotation.x) * easeFactorX;
        }
      }

      if (intersects.length > 0) {
        const pos = this.particles.geometry.attributes.position;
        const copy = this.geometryCopy.attributes.position;
        const colors = this.particles.geometry.attributes.customColor;
        const sizes = this.particles.geometry.attributes.size;

        const mx = intersects[0].point.x;
        const my = intersects[0].point.y;
        // const mz = intersects[0].point.z; // Not used

        for (let i = 0, l = pos.count; i < l; i++) {
          const initX = copy.getX(i);
          const initY = copy.getY(i);
          const initZ = copy.getZ(i);

          let px = pos.getX(i);
          let py = pos.getY(i);
          let pz = pos.getZ(i);

          // Set to white color
          this.colorChange.setRGB(1, 1, 1);
          colors.setXYZ(
            i,
            this.colorChange.r,
            this.colorChange.g,
            this.colorChange.b
          );
          colors.needsUpdate = true;

          sizes.array[i] = this.options.particleSize;
          sizes.needsUpdate = true;

          const dx = mx - px;
          const dy = my - py;
          const mouseDistance = this.distance(mx, my, px, py);
          const d = dx * dx + dy * dy;
          const f = -this.options.area / d;

          // Apply dissolve effect for mouse interactions or long press on touch
          if (this.buttonDown) {
            const t = Math.atan2(dy, dx);
            px -= f * Math.cos(t);
            py -= f * Math.sin(t);

            // Keep white color with slight animation
            const brightness = 0.8 + 0.2 * Math.sin(performance.now() * 0.002);
            this.colorChange.setRGB(brightness, brightness, brightness);
            colors.setXYZ(
              i,
              this.colorChange.r,
              this.colorChange.g,
              this.colorChange.b
            );
            colors.needsUpdate = true;

            if (
              px > initX + PARTICLE_DISTANCE_THRESHOLD.FAR ||
              px < initX - PARTICLE_DISTANCE_THRESHOLD.FAR ||
              py > initY + PARTICLE_DISTANCE_THRESHOLD.FAR ||
              py < initY - PARTICLE_DISTANCE_THRESHOLD.FAR
            ) {
              // White color for distant particles
              this.colorChange.setRGB(1, 1, 1);
              colors.setXYZ(
                i,
                this.colorChange.r,
                this.colorChange.g,
                this.colorChange.b
              );
              colors.needsUpdate = true;
            }
          } else {
            if (mouseDistance < this.options.area) {
              // Apply hover effect for both mouse and touch, even during scrolling
              if (i % 5 === 0) {
                const t = Math.atan2(dy, dx);
                // Use a stronger effect for touch devices to make it more noticeable
                // Adjust factor based on whether we're scrolling or not
                let moveFactor = MOUSE_MOVE_FACTOR;
                if (this.isTouchDevice) {
                  // Stronger effect for touch
                  moveFactor *= 1.5;
                  // Maintain effect during scrolling, but slightly reduced
                  if (this.isTouchScrolling) {
                    moveFactor *= 0.8; // 80% strength during scrolling
                  }
                }

                px -= moveFactor * Math.cos(t);
                py -= moveFactor * Math.sin(t);

                // White color for particles affected by mouse/touch
                this.colorChange.setRGB(1, 1, 1);
                colors.setXYZ(
                  i,
                  this.colorChange.r,
                  this.colorChange.g,
                  this.colorChange.b
                );
                colors.needsUpdate = true;

                sizes.array[i] = this.options.particleSize / 1.2;
                sizes.needsUpdate = true;
              } else {
                const t = Math.atan2(dy, dx);
                // Use a stronger effect for touch devices
                let effectMultiplier = this.isTouchDevice ? 1.2 : 1.0;
                // Adjust multiplier during scrolling
                if (this.isTouchDevice && this.isTouchScrolling) {
                  effectMultiplier *= 0.8; // 80% strength during scrolling
                }

                px += f * Math.cos(t) * effectMultiplier;
                py += f * Math.sin(t) * effectMultiplier;

                pos.setXYZ(i, px, py, pz);
                pos.needsUpdate = true;

                sizes.array[i] = this.options.particleSize * 1.3;
                sizes.needsUpdate = true;
              }

              if (
                px > initX + PARTICLE_DISTANCE_THRESHOLD.CLOSE ||
                px < initX - PARTICLE_DISTANCE_THRESHOLD.CLOSE ||
                py > initY + PARTICLE_DISTANCE_THRESHOLD.CLOSE ||
                py < initY - PARTICLE_DISTANCE_THRESHOLD.CLOSE
              ) {
                // White color for particles that have moved
                this.colorChange.setRGB(1, 1, 1);
                colors.setXYZ(
                  i,
                  this.colorChange.r,
                  this.colorChange.g,
                  this.colorChange.b
                );
                colors.needsUpdate = true;

                sizes.array[i] = this.options.particleSize / 1.8;
                sizes.needsUpdate = true;
              }
            }
          }

          // Apply stronger return force after touch release for faster reset
          const easeValue = this.hasLongPressed ? this.options.ease * 1.2 : this.options.ease;
          px += (initX - px) * easeValue;
          py += (initY - py) * easeValue;
          pz += (initZ - pz) * easeValue;

          pos.setXYZ(i, px, py, pz);
          pos.needsUpdate = true;
        }
      }
    }

    visibleHeightAtZDepth(depth, camera) {
      const cameraOffset = camera.position.z;
      if (depth < cameraOffset) depth -= cameraOffset;
      else depth += cameraOffset;

      const vFOV = (camera.fov * Math.PI) / 180;
      return 2 * Math.tan(vFOV / 2) * Math.abs(depth);
    }

    visibleWidthAtZDepth(depth, camera) {
      const height = this.visibleHeightAtZDepth(depth, camera);
      return height * camera.aspect;
    }

    distance(x1, y1, x2, y2) {
      return Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
    }

    updateText(newText, newTextSize) {
      // Update the text and size options
      this.options.text = newText;
      this.options.textSize = newTextSize || this.options.textSize;

      // Check if mobile view has changed
      const newIsMobileView = newText.includes('\n');
      if (newIsMobileView !== this.isMobileView) {
        this.isMobileView = newIsMobileView;
        // Update text position based on new view
        this.textYPosition = this.isMobileView ? MOBILE_TEXT_Y_POSITION : DESKTOP_TEXT_Y_POSITION;      }

      // Remove existing particles
      if (this.particles) {
        this.scene.remove(this.particles);
        this.particles.geometry.dispose();
        this.particles.material.dispose();
        this.particles = null;
      }

      // Recreate text with new text and size
      this.createText();
    }

    updateTextSize(newTextSize) {
      this.updateText(this.options.text, newTextSize);
    }

    // Method to update zoom based on scroll progress (0 to 1)
    updateZoom(progress) {
      if (progress === undefined || progress === null) {
        console.warn('Progress parameter is required for updateZoom');
        return false;
      }

      // Clamp progress between 0 and 1
      const clampedProgress = Math.max(0, Math.min(1, progress));

      // Get camera positions from constants
      const initialCameraPosition = new THREE.Vector3(...CAMERA_POSITION);
      const targetCameraPosition = new THREE.Vector3(...CAMERA_END_POSITION);

      // Add more pronounced easing for smoother transitions
      // Use a stronger easing function for more obvious effect
      const easeInOutCubic = (t) =>
        t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
      const easedProgress = easeInOutCubic(clampedProgress);

      // Interpolate camera position
      const newPosition = new THREE.Vector3().lerpVectors(
        initialCameraPosition,
        targetCameraPosition,
        easedProgress
      );

      // Update camera position
      this.camera.position.copy(newPosition);

      // Also adjust field of view slightly for more dramatic effect
      // Start with wider FOV and narrow as we zoom in
      const startFOV = CAMERA_FOV;
      const endFOV = CAMERA_FOV * 0.8; // 20% narrower at end
      this.camera.fov = startFOV - (startFOV - endFOV) * easedProgress;
      this.camera.updateProjectionMatrix();

      // Check if we've reached the target position (with a small threshold)
      const hasReachedTarget = this.hasReachedTargetPosition();

      // Log when reaching target position
      if (hasReachedTarget && !this._hasLoggedMaxZoom) {
        console.log('Target camera position reached');
        this._hasLoggedMaxZoom = true;
      } else if (!hasReachedTarget) {
        this._hasLoggedMaxZoom = false;
      }

      return hasReachedTarget;
    }

    // Check if maximum zoom has been reached
    hasReachedTargetPosition() {
      if (!this.camera) return false;

      const targetPosition = new THREE.Vector3(...CAMERA_END_POSITION);
      const currentPosition = this.camera.position;

      // Calculate distance to target position
      const distance = currentPosition.distanceTo(targetPosition);

      // Use a small threshold (0.5 units) to determine if we're close enough
      const POSITION_THRESHOLD = 0.5;
      return distance <= POSITION_THRESHOLD;
    }

    // Legacy method for compatibility
    startZoomAnimation() {
      console.log('Starting zoom animation in ParticleSystem');

      // Store initial camera position
      const initialCameraPosition = new THREE.Vector3().copy(this.camera.position);

      // Target position (zoomed in to the image)
      const targetCameraPosition = new THREE.Vector3(0, 0, 5);

      // Animation timing
      const startTime = performance.now();

      // Animation function
      const animateZoom = () => {
        const now = performance.now();
        const elapsed = now - startTime;
        const progress = Math.min(elapsed / ZOOM_DURATION, 1);

        // Ease in-out function
        const easeInOutCubic = (t) =>
          t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;

        const easedProgress = easeInOutCubic(progress);

        // Interpolate camera position
        const newPosition = new THREE.Vector3().lerpVectors(
          initialCameraPosition,
          targetCameraPosition,
          easedProgress
        );

        // Update camera position
        this.camera.position.copy(newPosition);

        // Continue animation until complete
        if (progress < 1) {
          requestAnimationFrame(animateZoom);
        } else {
          console.log('Zoom animation complete');
        }
      };

      // Start animation
      requestAnimationFrame(animateZoom);
    }

    setVisible(visible) {
      if (this.particles) {
        this.particles.visible = visible;
      }
      // Keep planeArea always invisible - it's only for interaction detection
      if (this.planeArea) {
        this.planeArea.visible = false;
      }
      if (this.imageMesh) {
        this.imageMesh.visible = visible;
      }
    }

    dispose() {
      if (this.cleanup) this.cleanup();
      if (this.particles) {
        this.scene.remove(this.particles);
        this.particles.geometry.dispose();
        this.particles.material.dispose();
      }
      if (this.planeArea) {
        this.scene.remove(this.planeArea);
        this.planeArea.geometry.dispose();
        this.planeArea.material.dispose();
      }
      if (this.imageMesh) {
        this.scene.remove(this.imageMesh);
        this.imageMesh.geometry.dispose();
        this.imageMesh.material.dispose();
      }
    }
  }