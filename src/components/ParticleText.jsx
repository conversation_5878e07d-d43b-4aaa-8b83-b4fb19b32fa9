import React, { useEffect, useRef, useState } from "react";
import * as THREE from "three";
import { useThree, use<PERSON>rame } from "@react-three/fiber";
import {
  DEFAULT_TEXT,
  MOBILE_TEXT,
  MOBILE_BREAKPOINT,
  PART<PERSON>LE_AMOUNT,
  PARTICLE_SIZE,
  PART<PERSON>LE_COLOR,
  INTERACTION_AREA,
  DEFAULT_EASE,
  MIN_TEXT_SIZE
} from "../constants/particleTextConstants";
import { useAssets } from "../contexts/AssetContext";
import { ParticleSystem } from "../classes/ParticleSystem";

const ParticleText = React.forwardRef(({ visible = true }, ref) => {
  // Get Three.js objects from React Three Fiber
  const { scene, camera, gl: renderer } = useThree();

  // Get preloaded assets from context
  const { assets, isLoaded, error } = useAssets();

  // Text format will change based on screen width
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < MOBILE_BREAKPOINT);
  const [displayText, setDisplayText] = useState(isMobileView ? MOBILE_TEXT : DEFAULT_TEXT);

  // Text size will be calculated based on window size
  const [textSize, setTextSize] = useState(calculateTextSize());

  // Calculate text size based on window dimensions
  function calculateTextSize() {
    // Base the text size on 80% of the window width
    const baseSize = window.innerWidth / 80;
    return Math.max(baseSize, MIN_TEXT_SIZE);
  }

  // Refs
  const particleSystemRef = useRef(null);

  // State - only keep error state since loading is handled by GlobalPreloader
  const [loadError, setLoadError] = useState(error ? true : false);

  // Initialize particle system with preloaded assets
  useEffect(() => {
    if (!isLoaded || !assets || loadError) return;

    try {
      // Create a local assets object with the specific assets we need
      const localAssets = {
        font: assets.font,
        particleTexture: assets.particleTexture,
        imageTexture: assets.defaultImage
      };

      // Make sure the image texture is properly configured
      if (localAssets.imageTexture) {
        localAssets.imageTexture.colorSpace = THREE.SRGBColorSpace;
        localAssets.imageTexture.minFilter = THREE.LinearMipmapLinearFilter;
        localAssets.imageTexture.magFilter = THREE.LinearFilter;
        localAssets.imageTexture.needsUpdate = true;
      } else {
        console.error("Image texture not found in preloaded assets");
        setLoadError(true);
        return;
      }

      // Clean up previous particle system if it exists
      if (particleSystemRef.current) {
        particleSystemRef.current.dispose();
      }

      // Create new particle system
      const particleSystem = new ParticleSystem(
        scene,
        localAssets.font,
        localAssets.particleTexture,
        localAssets.imageTexture,
        camera,
        renderer,
        {
          text: displayText,
          amount: PARTICLE_AMOUNT,
          particleSize: PARTICLE_SIZE,
          particleColor: PARTICLE_COLOR,
          textSize,
          area: INTERACTION_AREA,
          ease: DEFAULT_EASE,
        }
      );

      particleSystemRef.current = particleSystem;
      console.log("Particle system initialized successfully");
    } catch (error) {
      console.error("Error setting up particle system:", error);
      setLoadError(true);
    }
  }, [scene, camera, renderer, assets, isLoaded, displayText, textSize, loadError]);

  // Control visibility of particle system
  useEffect(() => {
    if (particleSystemRef.current) {
      particleSystemRef.current.setVisible(visible);
    }
  }, [visible]);

  // Handle rendering in the animation loop
  useFrame(() => {
    if (particleSystemRef.current) {
      particleSystemRef.current.render();
    }
  });

  // Handle resize events
  useEffect(() => {
    const handleResize = () => {
      // Check if we need to switch between mobile and desktop text format
      const newIsMobileView = window.innerWidth < MOBILE_BREAKPOINT;
      if (newIsMobileView !== isMobileView) {
        setIsMobileView(newIsMobileView);
        const newText = newIsMobileView ? MOBILE_TEXT : DEFAULT_TEXT;
        setDisplayText(newText);
      }

      // Update text size based on new window dimensions
      const newTextSize = calculateTextSize();
      setTextSize(newTextSize);

      // If particle system exists, update text with new size and format
      if (particleSystemRef.current) {
        particleSystemRef.current.updateText(displayText, newTextSize);
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);

      // Clean up particle system
      if (particleSystemRef.current) {
        try {
          particleSystemRef.current.dispose();
          particleSystemRef.current = null;
        } catch (e) {
          console.warn("Error disposing particle system:", e);
        }
      }
    };
  }, [displayText, isMobileView]);

  // Expose the updateZoom, hasReachedTargetPosition, and startZoomAnimation methods to the parent component
  React.useImperativeHandle(ref, () => ({
    // Update zoom based on scroll progress (0 to 1)
    updateZoom: (progress) => {
      if (particleSystemRef.current) {
        // Ensure progress is a number between 0 and 1
        const clampedProgress = Math.max(0, Math.min(1, progress || 0));
        const hasReachedTarget = particleSystemRef.current.updateZoom(clampedProgress);
        return hasReachedTarget;
      }
      return false;
    },

    // Check if camera has reached target position
    hasReachedTargetPosition: () => {
      if (particleSystemRef.current) {
        const hasReached = particleSystemRef.current.hasReachedTargetPosition();
        return hasReached;
      }
      return false;
    },

    // Legacy method for compatibility
    startZoomAnimation: () => {
      if (particleSystemRef.current) {
        particleSystemRef.current.startZoomAnimation();
      }
    }
  }));

  // In React Three Fiber, we return a mesh or group instead of a div
  return (
    <>
      {/* Error message - keep this for component-specific errors */}
      {loadError && (
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[1, 1, 1]} />
          <meshBasicMaterial color="red" />
        </mesh>
      )}

      {/* We don't need to return anything else since the particle system is added to the scene directly */}
    </>
  );
});



export default ParticleText;
