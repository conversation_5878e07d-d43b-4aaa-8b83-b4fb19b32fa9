import React, { useRef, useEffect } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { useAssets } from '../../contexts/AssetContext';
import { PARTICLE_COUNT, PARTICLE_SIZE, PARTICLE_DISTRIBUTION_RADIUS, PARTICLE_COLOR_1, PARTICLE_COLOR_2 } from '../../constants/simpleParticleConstants';

const ParticleSystem = () => {
  const { assets, isLoaded } = useAssets();
  const particlesRef = useRef();
  const particlePositions = useRef(new Float32Array(PARTICLE_COUNT * 3));
  const particleColors = useRef(new Float32Array(PARTICLE_COUNT * 3));

  useEffect(() => {
      const positions = particlePositions.current;
      const colors = particleColors.current;

      const color1 = new THREE.Color(PARTICLE_COLOR_1);
      const color2 = new THREE.Color(PARTICLE_COLOR_2);

      for (let i = 0; i < PARTICLE_COUNT; i++) {
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(2 * Math.random() - 1);
          const radius = Math.random() * PARTICLE_DISTRIBUTION_RADIUS;

          positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
          positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
          positions[i * 3 + 2] = radius * Math.cos(phi);

          const mixFactor = Math.random();
          const mixedColor = color1.clone().lerp(color2, mixFactor);

          colors[i * 3] = mixedColor.r;
          colors[i * 3 + 1] = mixedColor.g;
          colors[i * 3 + 2] = mixedColor.b;
      }
  }, []);

  const getParticleMaterial = () => {
      if (isLoaded && assets && assets.particleTexture) {
          return (
              <pointsMaterial
                  size={PARTICLE_SIZE}
                  sizeAttenuation={true}
                  depthWrite={false}
                  vertexColors={true}
                  blending={THREE.AdditiveBlending}
                  transparent={true}
                  opacity={0.8}
                  map={assets.particleTexture}
              />
          );
      }

      return (
          <pointsMaterial
              size={PARTICLE_SIZE}
              sizeAttenuation={true}
              depthWrite={false}
              vertexColors={true}
              blending={THREE.AdditiveBlending}
              transparent={true}
              opacity={0.8}
          />
      );
  };

  return (
      <points ref={particlesRef}>
          <bufferGeometry>
              <bufferAttribute
                  attach="attributes-position"
                  count={PARTICLE_COUNT}
                  array={particlePositions.current}
                  itemSize={3}
              />
              <bufferAttribute
                  attach="attributes-color"
                  count={PARTICLE_COUNT}
                  array={particleColors.current}
                  itemSize={3}
              />
          </bufferGeometry>
          {getParticleMaterial()}
      </points>
  );
};
export default ParticleSystem; 