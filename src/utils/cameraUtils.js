import * as THREE from 'three';

/**
 * Camera Utility Functions
 * Reusable functions for camera calculations and animations
 */

/**
 * Easing function for smooth animations
 */
export const easeInOutCubic = (t) => {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
};

/**
 * Linear interpolation between two values
 */
export const lerp = (start, end, alpha) => {
  return start * (1 - alpha) + end * alpha;
};

/**
 * Clamp a value between min and max
 */
export const clamp = (value, min, max) => {
  return Math.min(Math.max(value, min), max);
};

/**
 * Convert mouse coordinates to normalized device coordinates
 */
export const getMouseNDC = (clientX, clientY) => {
  return {
    x: (clientX / window.innerWidth) * 2 - 1,
    y: -(clientY / window.innerHeight) * 2 + 1
  };
};

/**
 * Calculate spherical coordinates from camera position relative to target
 */
export const getSphericalFromPosition = (cameraPosition, target) => {
  const offset = new THREE.Vector3().subVectors(cameraPosition, target);
  return new THREE.Spherical().setFromVector3(offset);
};

/**
 * Animate camera position between two points with easing
 */
export const animateCameraPosition = (
  currentPosition,
  targetPosition,
  progress,
  easingFunction = easeInOutCubic
) => {
  const easedProgress = easingFunction(progress);
  return new THREE.Vector3().lerpVectors(
    currentPosition,
    targetPosition,
    easedProgress
  );
};

/**
 * Apply smooth interpolation to spherical coordinates
 */
export const updateSphericalCoordinates = (
  currentSpherical,
  targetPhi,
  targetTheta,
  targetRadius,
  easing = 0.1
) => {
  currentSpherical.phi += (targetPhi - currentSpherical.phi) * easing;
  currentSpherical.theta += (targetTheta - currentSpherical.theta) * easing;
  currentSpherical.radius += (targetRadius - currentSpherical.radius) * easing;
  
  // Clamp phi to prevent camera flipping
  currentSpherical.phi = clamp(currentSpherical.phi, 0.01, Math.PI - 0.01);
  
  return currentSpherical;
};

/**
 * Check if two positions are approximately equal
 */
export const positionsEqual = (pos1, pos2, tolerance = 0.01) => {
  return pos1.distanceTo(pos2) < tolerance;
};

export default {
  easeInOutCubic,
  lerp,
  clamp,
  getMouseNDC,
  getSphericalFromPosition,
  animateCameraPosition,
  updateSphericalCoordinates,
  positionsEqual
}; 