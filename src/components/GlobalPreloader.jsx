import React, { useState, useEffect, useRef } from 'react';
import * as THREE from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import {
  FONT_URL,
  PARTICLE_IMAGE_URL,
  DEFAULT_IMAGE_URL
} from '../constants/particleTextConstants';
import { PORTFOLIO_ITEMS } from '../constants/simpleParticleConstants';
import { AssetProvider } from '../contexts/AssetContext';

const GlobalPreloader = ({ children }) => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [preloadedAssets, setPreloadedAssets] = useState({});
  const loadingManagerRef = useRef(null);

  useEffect(() => {
    const preloadAssets = async () => {
      try {
        setIsLoading(true);
        setProgress(0);
        setError(null);

        // Create a loading manager to track overall progress
        const loadingManager = new THREE.LoadingManager(
          () => {
            // All assets loaded
            console.log('All assets loaded successfully');
            setIsLoading(false);
            setProgress(100);
          },
          (url, loaded, total) => {
            // Progress update
            const progressValue = (loaded / total) * 100;
            console.log(`Loading progress: ${Math.round(progressValue)}% (${url})`);
            setProgress(progressValue);
          },
          (url) => {
            // Error handler
            console.error(`Error loading asset: ${url}`);
            setError(`Failed to load asset: ${url}`);
          }
        );

        loadingManagerRef.current = loadingManager;

        // Create texture loader with the loading manager
        const textureLoader = new THREE.TextureLoader(loadingManager);
        const fontLoader = new FontLoader(loadingManager);

        // Collect all assets to preload
        const assets = {};

        // Load font
        const fontPromise = new Promise((resolve, reject) => {
          fontLoader.load(
            FONT_URL,
            (font) => {
              console.log('Font loaded successfully');
              assets.font = font;
              resolve();
            },
            undefined,
            (error) => {
              console.error('Error loading font:', error);
              reject(error);
            }
          );
        });

        // Load particle texture
        const particleTexturePromise = new Promise((resolve, reject) => {
          textureLoader.load(
            PARTICLE_IMAGE_URL,
            (texture) => {
              console.log('Particle texture loaded successfully');
              assets.particleTexture = texture;
              resolve();
            },
            undefined,
            (error) => {
              console.error('Error loading particle texture:', error);
              reject(error);
            }
          );
        });

        // Load default image texture
        const defaultImagePromise = new Promise((resolve, reject) => {
          textureLoader.load(
            DEFAULT_IMAGE_URL,
            (texture) => {
              console.log('Default image loaded successfully');
              texture.colorSpace = THREE.SRGBColorSpace;
              texture.minFilter = THREE.LinearMipmapLinearFilter;
              texture.magFilter = THREE.LinearFilter;
              texture.needsUpdate = true;
              assets.defaultImage = texture;
              resolve();
            },
            undefined,
            (error) => {
              console.error('Error loading default image:', error);
              reject(error);
            }
          );
        });

        // Load portfolio project images
        const portfolioImagePromises = PORTFOLIO_ITEMS.map((item) => {
          return new Promise((resolve) => {
            if (!item.image) {
              console.warn(`No image specified for portfolio item: ${item.title}`);
              resolve();
              return;
            }

            textureLoader.load(
              item.image,
              (texture) => {
                console.log(`Portfolio image loaded: ${item.image}`);
                texture.colorSpace = THREE.SRGBColorSpace;
                texture.minFilter = THREE.LinearMipmapLinearFilter;
                texture.magFilter = THREE.LinearFilter;
                texture.needsUpdate = true;

                // Store in assets with a key based on the image path
                const key = `portfolio_${item.id}`;
                assets[key] = texture;
                resolve();
              },
              undefined,
              (error) => {
                console.error(`Error loading portfolio image ${item.image}:`, error);
                // Don't reject the whole loading process for a single portfolio image
                // Just log the error and continue
                resolve();
              }
            );
          });
        });

        // Combine all promises
        const allPromises = [
          fontPromise,
          particleTexturePromise,
          defaultImagePromise,
          ...portfolioImagePromises
        ];

        // Wait for all assets to load
        await Promise.all(allPromises);

        // Store the loaded assets
        setPreloadedAssets(assets);
        console.log('All assets preloaded and stored in context');

      } catch (err) {
        console.error('Preloading error:', err);
        setError(err.message || 'Failed to load assets');
      }
    };

    preloadAssets();

    // Cleanup function
    return () => {
      // Cancel any pending loads if component unmounts
      if (loadingManagerRef.current) {
        loadingManagerRef.current.onLoad = null;
        // loadingManagerRef.current.onProgress = null;
        loadingManagerRef.current.onError = null;
      }
    };
  }, []);

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black z-50">
        <div className="text-center p-6 bg-red-900 rounded-lg">
          <h2 className="text-white text-xl mb-4">Preloading Error</h2>
          <p className="text-white mb-4">{error}</p>
          <button
            className="px-4 py-2 bg-white text-red-900 rounded hover:bg-gray-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black z-50">
        <div className="text-center">
          <div className="text-white text-xl mb-4">Loading Assets... {Math.round(progress)}%</div>
          <div className="w-64 h-2 bg-gray-800 rounded-full overflow-hidden mx-auto">
            <div
              className="h-full bg-purple-600 transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </div>
    );
  }

  // Provide the preloaded assets to all child components
  return (
    <AssetProvider
      assets={preloadedAssets}
      isLoaded={!isLoading}
      error={error}
    >
      {children}
    </AssetProvider>
  );
};

export default GlobalPreloader;