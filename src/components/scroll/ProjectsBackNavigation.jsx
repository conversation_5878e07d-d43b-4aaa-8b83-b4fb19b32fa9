import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useScroll } from '@react-three/drei';
import { useNavigate } from 'react-router-dom';

/**
 * Projects Back Navigation Handler
 * Handles scroll-based navigation from Projects back to Home
 * Works in coordination with TileScrollHandler
 */
const ProjectsBackNavigation = ({ enabled = true }) => {
  const data = useScroll();
  const navigate = useNavigate();
  const hasNavigatedRef = useRef(false);
  const prevScrollRef = useRef(0);

  useFrame(() => {
    if (!enabled || !data) return;
    
    const scrollProgress = data.offset;
    const scrollDelta = scrollProgress - prevScrollRef.current;
    
    // Detect upward scroll when at the beginning (top of projects page)
    // OR when near the end (after all tiles)
    const isUpwardScroll = scrollDelta < -0.002; // More sensitive
    const isAtBeginning = scrollProgress < 0.15;  // Larger detection zone
    const isAtEnd = scrollProgress > 0.85;
    
    if (isUpwardScroll && (isAtBeginning || isAtEnd) && !hasNavigatedRef.current) {
      hasNavigatedRef.current = true;
      const location = isAtBeginning ? 'beginning' : 'end';
      console.log(`Navigating back to home from projects ${location}`);
      setTimeout(() => {
        navigate('/');
      }, 100);
    }
    
    prevScrollRef.current = scrollProgress;
  });

  return null;
};

export default ProjectsBackNavigation; 