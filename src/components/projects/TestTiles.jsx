import React, { useRef, useState } from "react"
import { useFrame } from "@react-three/fiber"
import { Text, Billboard } from "@react-three/drei"
import * as THREE from "three"
import { TILE_POSITIONS } from "../../constants/projectsConstants"

const TestTiles = () => {
  return (
    <group>
      {TILE_POSITIONS.map((tile) => (
        <Tile key={tile.id} tile={tile} />
      ))}
    </group>
  )
}

function Tile({ tile }) {
  const matRef = useRef()
  const textRef = useRef()
  const rectRef = useRef()
  const hologramMatRef = useRef()
  const [hovered, setHovered] = useState(false)
  const [progress, setProgress] = useState(0) // 0 = text, 1 = rectangle

  useFrame(({ clock }, delta) => {
    if (matRef.current) {
      matRef.current.uniforms.time.value = clock.elapsedTime
    }

    // Smoothly animate morph progress with easing
    const target = hovered ? 1 : 0
    setProgress((p) => THREE.MathUtils.lerp(p, target, hovered ? 0.15 : 0.08))

    // Vibrating text effect with glitch
    if (textRef.current) {
      const intensity = hovered ? (1 - progress) * 0.1 : 0
      const glitchIntensity = hovered ? (1 - progress) * 0.05 : 0

      // Add random glitch offsets
      const glitchX = (Math.random() - 0.5) * glitchIntensity
      const glitchY = (Math.random() - 0.5) * glitchIntensity

      textRef.current.position.x = Math.sin(clock.elapsedTime * 80) * intensity + glitchX
      textRef.current.position.y = Math.cos(clock.elapsedTime * 80) * intensity + glitchY
      textRef.current.scale.setScalar(1 + intensity * 2)

      // Flickering opacity during transition
      const flicker = hovered && progress < 0.8 ? Math.random() * 0.3 : 0
      textRef.current.material.opacity = Math.max(0, (1 - progress) - flicker)
    }

    // Hologram rectangle effect
    if (rectRef.current && hologramMatRef.current) {
      // Update hologram shader uniforms
      hologramMatRef.current.uniforms.time.value = clock.elapsedTime
      hologramMatRef.current.uniforms.progress.value = progress
      hologramMatRef.current.uniforms.opacity.value = progress

      // Dramatic scale animation with overshoot
      const scaleProgress = Math.min(1, progress * 1.2)
      const overshoot = scaleProgress > 1 ? Math.sin((scaleProgress - 1) * Math.PI * 4) * 0.1 : 0
      const finalScale = 0.3 + scaleProgress * 0.7 + overshoot
      rectRef.current.scale.setScalar(finalScale)

      // Add rotation during materialization
      if (progress > 0 && progress < 1) {
        rectRef.current.rotation.z = Math.sin(progress * Math.PI * 2) * 0.1
      } else {
        rectRef.current.rotation.z = 0
      }
    }
  })

  return (
    <group position={tile.position}>
      <Billboard position={[0, 0, 0.15]}>
        {/* ---------- Glossy vibrating text ---------- */}
        <Text
          ref={textRef}
          font="/fonts/Azonix.otf"
          fontSize={1}
          anchorX="center"
          anchorY="middle"
          maxWidth={8}
          bevelEnabled
          bevelThickness={0.02}
          bevelSize={0.03}
          bevelSegments={6}
          onPointerOver={() => setHovered(true)}
          onPointerOut={() => setHovered(false)}
        >
          {tile.name}
          <shaderMaterial
            ref={matRef}
            attach="material"
            transparent
            uniforms={{
              time: { value: 0 },
              baseColor: { value: new THREE.Color("#bfbfbf") },
            }}
            vertexShader={`
              varying vec2 vUv;
              void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
              }
            `}
            fragmentShader={`
              uniform float time;
              uniform vec3 baseColor;
              varying vec2 vUv;
              void main() {
                float sweep = smoothstep(0.0, 0.15, abs(vUv.x + vUv.y - mod(time*0.5, 2.0)));
                vec3 highlight = vec3(1.0, 1.0, 1.0);
                vec3 color = mix(baseColor, highlight, sweep);
                gl_FragColor = vec4(color, 1.0);
              }
            `}
          />
        </Text>

        {/* ---------- Hologram Rectangle ---------- */}
        <mesh ref={rectRef}>
          <boxGeometry args={[8, 2, 0.1]} />
          <shaderMaterial
            ref={hologramMatRef}
            attach="material"
            transparent
            side={THREE.DoubleSide}
            uniforms={{
              time: { value: 0 },
              progress: { value: 0 },
              opacity: { value: 0 },
            }}
            vertexShader={`
              varying vec2 vUv;
              varying vec3 vPosition;
              uniform float time;
              uniform float progress;

              void main() {
                vUv = uv;
                vPosition = position;

                // Add some vertex displacement for materialization effect
                vec3 pos = position;
                if (progress > 0.0 && progress < 1.0) {
                  float noise = sin(pos.x * 10.0 + time * 5.0) * sin(pos.y * 15.0 + time * 3.0);
                  pos += normal * noise * 0.1 * (1.0 - progress);
                }

                gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
              }
            `}
            fragmentShader={`
              uniform float time;
              uniform float progress;
              uniform float opacity;
              varying vec2 vUv;
              varying vec3 vPosition;

              void main() {
                // Base hologram color
                vec3 baseColor = vec3(0.2, 0.6, 1.0);
                vec3 accentColor = vec3(0.8, 0.4, 1.0);

                // Scanlines effect
                float scanlines = sin(vUv.y * 50.0 + time * 10.0) * 0.5 + 0.5;
                scanlines = pow(scanlines, 3.0) * 0.3;

                // Horizontal sweep effect during materialization
                float sweep = smoothstep(0.0, 0.1, vUv.y - (progress * 1.2 - 0.1));
                sweep *= smoothstep(0.1, 0.0, vUv.y - (progress * 1.2 + 0.1));

                // Interference pattern
                float interference = sin(vUv.x * 20.0 + time * 8.0) * sin(vUv.y * 25.0 + time * 6.0);
                interference = interference * 0.1 + 0.9;

                // Edge glow
                float edgeGlow = 1.0 - smoothstep(0.0, 0.1, min(min(vUv.x, 1.0 - vUv.x), min(vUv.y, 1.0 - vUv.y)));
                edgeGlow = pow(edgeGlow, 2.0);

                // Flickering effect
                float flicker = sin(time * 30.0) * 0.1 + 0.9;
                if (progress < 0.8) {
                  flicker *= (sin(time * 60.0) * 0.2 + 0.8);
                }

                // Combine effects
                vec3 color = mix(baseColor, accentColor, scanlines + sweep * 2.0);
                color += edgeGlow * accentColor * 0.5;
                color *= interference * flicker;

                // Materialization alpha
                float alpha = opacity * progress;
                if (progress < 1.0) {
                  alpha *= (0.7 + sweep * 0.3);
                }

                gl_FragColor = vec4(color, alpha * 0.8);
              }
            `}
          />
        </mesh>
      </Billboard>
    </group>
  )
}

export default TestTiles
