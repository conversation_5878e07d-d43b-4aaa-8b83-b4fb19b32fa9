import React from 'react';
import { useFrame } from '@react-three/fiber';
import { useScroll } from '@react-three/drei';
import { useNavigate } from 'react-router-dom';
import { useRef } from 'react';

/**
 * Home Page Scroll Handler
 * Manages scroll-based navigation from Home to Projects with particle text zoom
 */
const HomeScrollHandler = ({ particleTextRef, onScrollProgress }) => {
  const data = useScroll();
  const navigate = useNavigate();
  const hasNavigatedRef = useRef(false);
  const prevOffsetRef = useRef(0);
  const lastUpdateTimeRef = useRef(0);

  useFrame(() => {
    const scrollProgress = data.offset;
    const currentTime = performance.now();

    // Update particle text zoom animation
    if (particleTextRef.current) {
      const hasReachedTarget = particleTextRef.current.updateZoom(scrollProgress);

      // Navigate to projects when zoom animation completes
      if (hasReachedTarget && !hasNavigatedRef.current) {
        hasNavigatedRef.current = true;
        setTimeout(() => {
          navigate('/projects');
        }, 100);
      }
    }

    // Throttle updates to the parent component to avoid excessive re-renders
    // Only update every 16ms (roughly 60fps) for smoother performance
    if (currentTime - lastUpdateTimeRef.current > 16) {
      if (onScrollProgress) {
        onScrollProgress(scrollProgress);
      }
      lastUpdateTimeRef.current = currentTime;

      if (Math.abs(scrollProgress - prevOffsetRef.current) > 0.01) {
        prevOffsetRef.current = scrollProgress;
      }
    }
  });

  return null;
};

export default HomeScrollHandler; 