import React, { createContext, useContext } from 'react';

// Create a context to store and provide preloaded assets
const AssetContext = createContext({
  assets: {},
  isLoaded: false,
  error: null
});

// Custom hook to use the asset context
export const useAssets = () => useContext(AssetContext);

// Provider component
export const AssetProvider = ({ children, assets, isLoaded, error }) => {
  return (
    <AssetContext.Provider value={{ assets, isLoaded, error }}>
      {children}
    </AssetContext.Provider>
  );
};

export default AssetContext;
