import React, { useRef, useState, Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { PerspectiveCamera, ScrollControls } from '@react-three/drei';
import * as THREE from 'three';
import ParticleText from '../components/ParticleText';
import ProjectsScene from '../components/projects/ProjectsScene';
import UnifiedScrollHandler from '../components/scroll/UnifiedScrollHandler';

const Unified = () => {
  const particleTextRef = useRef(null);
  const [currentScene, setCurrentScene] = useState('home'); // Keep for UI hints only
  
  // Debug scene changes - now only for UI feedback
  const handleSceneChange = (newScene) => {
    if (newScene !== currentScene) {
      console.log(`UI Scene change: ${currentScene} -> ${newScene}`);
      setCurrentScene(newScene);
    }
  };

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden">
      <Canvas
        shadows
        gl={{ antialias: true, alpha: false, powerPreference: 'high-performance' }}
        onCreated={({ gl }) => {
          gl.setClearColor(new THREE.Color(0x000000));
          gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }}
      >
        <PerspectiveCamera makeDefault fov={75} near={0.1} far={1000} />
        <Suspense fallback={null}>
          {/* Use 6 pages for generous scroll budget across both sections */}
          <ScrollControls pages={6} damping={0.25}>
            {/* Always render both scenes but control their visibility */}
            <ParticleText 
              ref={particleTextRef} 
              visible={currentScene === 'home'}
            />

            <ProjectsScene 
              disableCameraController={true}
              visible={currentScene === 'projects'}
            />

            {/* Unified scroll orchestrator */}
            <UnifiedScrollHandler 
              particleTextRef={particleTextRef} 
              onSceneChange={handleSceneChange}
            />
          </ScrollControls>
        </Suspense>
      </Canvas>

      {/* UI Hints */}
      <div className="absolute top-4 right-10 text-white text-xs bg-black bg-opacity-50 p-2 rounded backdrop-blur-sm">
        <span className="mb-1">
          scroll to explore
        </span>
      </div>
    </div>
  );
};

export default Unified; 