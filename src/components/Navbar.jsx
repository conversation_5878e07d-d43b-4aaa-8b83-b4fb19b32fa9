import React from "react";
import { Link, useLocation } from "react-router-dom";

const Navbar = () => {
  const location = useLocation();

  const navLinkClass = (path) =>
    `relative inline-block !text-white text-sm
     ${location.pathname === path ? "after:w-full" : "after:w-0"}
     after:absolute after:left-0 after:bottom-0 after:h-[1px]
     after:bg-white after:transition-all after:duration-300
     hover:after:w-full`;

  return (
    <nav className="fixed top-0 left-0 w-full z-50 bg-transparent">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-start h-14 space-x-6">
          {/* Menu items */}
          <Link to="/" className={navLinkClass("/")}>
            Home
          </Link>
          <Link to="/projects" className={navLinkClass("/projects")}>
            Projects
          </Link>
          <Link to="/about" className={navLinkClass("/about")}>
            About
          </Link>
          <Link to="/contact" className={navLinkClass("/contact")}>
            Contact
          </Link>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
