import React, { useRef, useState } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import * as THREE from 'three';

/**
 * Individual Project Tile Component
 * Represents a floating project card in 3D space
 */
const ProjectTile = ({ 
  project, 
  position = [0, 0, 0], 
  scale = 1,
  onHover,
  onClick 
}) => {
  const meshRef = useRef();
  const textRef = useRef();
  const [hovered, setHovered] = useState(false);
  const [clicked, setClicked] = useState(false);
  const { camera } = useThree();

  // Floating animation
  useFrame((state) => {
    if (meshRef.current) {
      const time = state.clock.getElapsedTime();
      meshRef.current.position.y = position[1] + Math.sin(time * 0.5 + position[0]) * 0.1;
      meshRef.current.rotation.y = Math.sin(time * 0.3) * 0.1;
      
      // Scale based on hover state
      const targetScale = hovered ? scale * 1.1 : scale;
      meshRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale }, 0.1);
    }

    // Make text always face camera
    if (textRef.current) {
      textRef.current.lookAt(camera.position);
    }
  });

  const handlePointerOver = (event) => {
    event.stopPropagation();
    setHovered(true);
    document.body.style.cursor = 'pointer';
    if (onHover) onHover(project, true);
  };

  const handlePointerOut = (event) => {
    event.stopPropagation();
    setHovered(false);
    document.body.style.cursor = 'auto';
    if (onHover) onHover(project, false);
  };

  const handleClick = (event) => {
    event.stopPropagation();
    setClicked(!clicked);
    if (onClick) onClick(project);
  };

  return (
    <group position={position}>
      {/* Main tile */}
      <mesh
        ref={meshRef}
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        onClick={handleClick}
      >
        <boxGeometry args={[2, 1.5, 0.1]} />
        <meshStandardMaterial
          color={hovered ? '#4f46e5' : '#1e1b4b'}
          transparent
          opacity={0.8}
          emissive={hovered ? '#312e81' : '#000000'}
        />
      </mesh>

      {/* Project title */}
      <Text
        ref={textRef}
        position={[0, 0.3, 0.1]}
        fontSize={0.4}
        color="white"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
      >
        {project.title}
      </Text>

      {/* Project description */}
      <Text
        position={[0, -0.1, 0.1]}
        fontSize={0.2}
        color="#94a3b8"
        anchorX="center"
        anchorY="middle"
        maxWidth={1.8}
      >
        {project.description}
      </Text>

      {/* Connection lines to center (optional visual effect) */}
      {hovered && (
        <line>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={2}
              array={new Float32Array([
                position[0], position[1], position[2],
                0, 0, 0
              ])}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color="#4f46e5" opacity={0.3} transparent />
        </line>
      )}
    </group>
  );
};

export default ProjectTile; 