import React, { useRef, useState } from "react"
import { useFrame } from "@react-three/fiber"
import { Text, Billboard } from "@react-three/drei"
import * as THREE from "three"
import { TILE_POSITIONS } from "../../constants/projectsConstants"

const TestTiles = () => {
  return (
    <group>
      {TILE_POSITIONS.map((tile) => (
        <Tile key={tile.id} tile={tile} />
      ))}
    </group>
  )
}

function Tile({ tile }) {
  const matRef = useRef()
  const textRef = useRef()
  const rectRef = useRef()
  const [hovered, setHovered] = useState(false)
  const [progress, setProgress] = useState(0) // 0 = text, 1 = rectangle

  useFrame(({ clock }, delta) => {
    if (matRef.current) {
      matRef.current.uniforms.time.value = clock.elapsedTime
    }

    // Smoothly animate morph progress
    const target = hovered ? 1 : 0
    setProgress((p) => THREE.MathUtils.lerp(p, target, 0.08))

    // Vibrating text effect
    if (textRef.current) {
      const intensity = hovered ? (1 - progress) * 0.05 : 0
      textRef.current.position.x = Math.sin(clock.elapsedTime * 40) * intensity
      textRef.current.position.y = Math.cos(clock.elapsedTime * 30) * intensity
      textRef.current.scale.setScalar(1 + intensity * 2)
      textRef.current.material.opacity = 1 - progress
    }

    // Rectangle fade-in
    if (rectRef.current) {
      rectRef.current.material.opacity = progress
      rectRef.current.scale.setScalar(0.8 + progress * 0.2)
    }
  })

  return (
    <group position={tile.position}>
      <Billboard position={[0, 0, 0.15]}>
        {/* ---------- Glossy vibrating text ---------- */}
        <Text
          ref={textRef}
          font="/fonts/Azonix.otf"
          fontSize={1}
          anchorX="center"
          anchorY="middle"
          maxWidth={8}
          bevelEnabled
          bevelThickness={0.02}
          bevelSize={0.03}
          bevelSegments={6}
          onPointerOver={() => setHovered(true)}
          onPointerOut={() => setHovered(false)}
        >
          {tile.name}
          <shaderMaterial
            ref={matRef}
            attach="material"
            transparent
            uniforms={{
              time: { value: 0 },
              baseColor: { value: new THREE.Color("#bfbfbf") },
            }}
            vertexShader={`
              varying vec2 vUv;
              void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
              }
            `}
            fragmentShader={`
              uniform float time;
              uniform vec3 baseColor;
              varying vec2 vUv;
              void main() {
                float sweep = smoothstep(0.0, 0.15, abs(vUv.x + vUv.y - mod(time*0.5, 2.0)));
                vec3 highlight = vec3(1.0, 1.0, 1.0);
                vec3 color = mix(baseColor, highlight, sweep);
                gl_FragColor = vec4(color, 1.0);
              }
            `}
          />
        </Text>

        {/* ---------- Rectangle (hologram placeholder) ---------- */}
        <mesh ref={rectRef}>
          <boxGeometry args={[8, 2, 0.1]} />
          <meshStandardMaterial
            color="#b3b3b3"
            emissive="#4f46e5"
            emissiveIntensity={0.7}
            transparent
            opacity={0}
          />
        </mesh>
      </Billboard>
    </group>
  )
}

export default TestTiles
