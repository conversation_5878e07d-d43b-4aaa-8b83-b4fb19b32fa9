import React, { useRef, useState } from "react"
import { useFrame } from "@react-three/fiber"
import { Text, Billboard } from "@react-three/drei"
import * as THREE from "three"
import { TILE_POSITIONS } from "../../constants/projectsConstants"

const TestTiles = () => {
  return (
    <group>
      {TILE_POSITIONS.map((tile) => (
        <Tile key={tile.id} tile={tile} />
      ))}
    </group>
  )
}

function Tile({ tile }) {
  const matRef = useRef()
  const textRef = useRef()
  const rectRef = useRef()
  const [hovered, setHovered] = useState(false)
  const [progress, setProgress] = useState(0) // 0 = text, 1 = rectangle

  useFrame(({ clock }) => {
    if (matRef.current) {
      matRef.current.uniforms.time.value = clock.elapsedTime
    }

    // Quick transition with jitter
    const target = hovered ? 1 : 0
    setProgress((p) => THREE.MathUtils.lerp(p, target, hovered ? 0.4 : 0.3))

    // Text visibility and effects - only show when not hovered
    if (textRef.current) {
      if (!hovered) {
        // Normal text state when not hovered
        textRef.current.position.x = 0
        textRef.current.position.y = 0
        textRef.current.scale.setScalar(1)
        textRef.current.material.opacity = 1
        textRef.current.visible = true
      } else {
        // Intense glitch out effect when transitioning to rectangle
        const intensity = (1 - progress) * 0.3
        const glitchIntensity = (1 - progress) * 0.2

        // Add intense random glitch offsets
        const glitchX = (Math.random() - 0.5) * glitchIntensity
        const glitchY = (Math.random() - 0.5) * glitchIntensity

        textRef.current.position.x = Math.sin(clock.elapsedTime * 120) * intensity + glitchX
        textRef.current.position.y = Math.cos(clock.elapsedTime * 120) * intensity + glitchY
        textRef.current.scale.setScalar(1 + intensity * 3)

        // Quick flickering opacity during transition
        const flicker = progress < 0.5 ? Math.random() * 0.5 : 0
        const opacity = Math.max(0, (1 - progress) - flicker)
        textRef.current.material.opacity = opacity
        textRef.current.visible = opacity > 0.01
      }
    }

    // Rectangle effect - only show when hovered
    if (rectRef.current) {
      if (hovered) {
        // Quick scale animation with jitter
        const jitterX = (Math.random() - 0.5) * 0.1 * (1 - progress)
        const jitterY = (Math.random() - 0.5) * 0.1 * (1 - progress)
        const jitterZ = (Math.random() - 0.5) * 0.05 * (1 - progress)

        rectRef.current.scale.set(1 + jitterX, 1 + jitterY, 1 + jitterZ)

        // Add jitter rotation during materialization
        const rotationJitter = (Math.random() - 0.5) * 0.2 * (1 - progress)
        rectRef.current.rotation.z = rotationJitter

        // Add position jitter
        const posJitterX = (Math.random() - 0.5) * 0.1 * (1 - progress)
        const posJitterY = (Math.random() - 0.5) * 0.1 * (1 - progress)
        rectRef.current.position.set(posJitterX, posJitterY, 0)

        rectRef.current.visible = true
      } else {
        // Hide rectangle when not hovered
        rectRef.current.visible = false
        rectRef.current.scale.setScalar(1)
        rectRef.current.rotation.z = 0
        rectRef.current.position.set(0, 0, 0)
      }
    }
  })

  return (
    <group position={tile.position}>
      <Billboard position={[0, 0, 0.15]}>
        {/* ---------- Glossy vibrating text ---------- */}
        <Text
          ref={textRef}
          font="/fonts/Azonix.otf"
          fontSize={1}
          anchorX="center"
          anchorY="middle"
          maxWidth={8}
          bevelEnabled
          bevelThickness={0.02}
          bevelSize={0.03}
          bevelSegments={6}
          onPointerOver={() => setHovered(true)}
          onPointerOut={() => setHovered(false)}
          onClick={() => window.open('https://google.com', '_blank')}
        >
          {tile.name}
          <shaderMaterial
            ref={matRef}
            attach="material"
            transparent
            uniforms={{
              time: { value: 0 },
              baseColor: { value: new THREE.Color("#bfbfbf") },
            }}
            vertexShader={`
              varying vec2 vUv;
              void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
              }
            `}
            fragmentShader={`
              uniform float time;
              uniform vec3 baseColor;
              varying vec2 vUv;
              void main() {
                float sweep = smoothstep(0.0, 0.15, abs(vUv.x + vUv.y - mod(time*0.5, 2.0)));
                vec3 highlight = vec3(1.0, 1.0, 1.0);
                vec3 color = mix(baseColor, highlight, sweep);
                gl_FragColor = vec4(color, 1.0);
              }
            `}
          />
        </Text>

        {/* ---------- Solid Gray Rectangle ---------- */}
        <mesh ref={rectRef} key={`rect-${tile.id}`}>
          <boxGeometry args={[8, 2, 0.1]} />
          <meshStandardMaterial
            color="#808080"
            transparent={false}
          />
        </mesh>
      </Billboard>
    </group>
  )
}

export default TestTiles
