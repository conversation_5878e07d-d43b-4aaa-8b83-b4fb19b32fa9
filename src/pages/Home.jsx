import React, { useRef } from "react";
import { Canvas } from "@react-three/fiber";
import { ScrollControls } from "@react-three/drei";
import ParticleText from "../components/ParticleText";
import HomeScrollHandler from "../components/scroll/HomeScrollHandler";
import { motion as Motion, useMotionValue, useTransform } from "framer-motion";

const Home = () => {
  const sceneContainerRef = useRef(null);
  const particleTextRef = useRef(null);
  const scrollY = useMotionValue(0);
  const opacity = useTransform(scrollY, [0, 0.1], [1, 0]);
  const translateY = useTransform(scrollY, [0, 0.1], [0, 20]);

  return (
    <div className="relative w-full h-screen bg-black overflow-hidden flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a001a] to-black"></div>
      <div className="absolute inset-0 z-10" ref={sceneContainerRef}>
        <Canvas>
          <ScrollControls pages={1.5} damping={0.25}>
            {/* Scene content */}
            <ParticleText ref={particleTextRef} />
            <HomeScrollHandler
              particleTextRef={particleTextRef}
              onScrollProgress={(progress) => scrollY.set(progress)}
            />
          </ScrollControls>
        </Canvas>
      </div>
      <div className="absolute bottom-25 sm:bottom-0 left-0 right-0 z-30 text-center pb-12">
        <Motion.div
          className="relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          style={{ opacity, y: translateY }}
        >
          <span className="text-xl tracking-widest block text-white">
            FULL STACK DEVELOPER
          </span>
        </Motion.div>
      </div>

      {/* Info with blinking effect */}
      <Motion.div
        className="absolute bottom-25 sm:top-4 sm:right-4 text-white text-xs p-2 rounded"
        animate={{
          opacity: [1, 0.5, 1],
          textShadow: [
            "0 0 0px rgba(255,255,255,0)",
            "0 0 5px rgba(255,255,255,0.5)",
            "0 0 0px rgba(255,255,255,0)",
          ],
        }}
        transition={{
          duration: 2.5,
          ease: "easeInOut",
          repeat: Infinity,
          repeatType: "loop",
        }}
      >
        {/* Show full text on sm and above */}
        <span className="bg-transparent hidden sm:inline">
          Let me take you through a journey | Scroll to explore
        </span>
        {/* Show only "Scroll to explore" on mobile */}
        <span className="bg-transparent inline sm:hidden">Scroll to explore</span>
      </Motion.div>
    </div>
  );
};

export default Home;
