import React, { useRef, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { 
  easeInOutCubic, 
  clamp, 
  getMouseNDC, 
  getSphericalFromPosition,
  updateSphericalCoordinates 
} from '../../utils/cameraUtils';

/**
 * Reusable Camera Controller Component
 * Handles mouse interaction, dragging, and smooth camera movement
 */
const CameraController = ({
  initialPosition = [0, 0, 25],
  targetPosition = [0, 0, 25],
  mouseMoveEnabled = true,
  dragEnabled = true,
  mouseMoveStrength = 0.1,
  dragSensitivity = 0.01,
  maxZoom = 150,
  minZoom = 20,
  mouseFollowEasing = 0.03,
  dragEasing = 0.08,
  zoomEasing = 0.1,
  autoTransition = false,
  transitionDuration = 1000
}) => {
  const { camera, gl } = useThree();
  
  // Interaction state
  const isDragging = useRef(false);
  const previousMousePosition = useRef({ x: 0, y: 0 });
  const mousePosition = useRef({ x: 0, y: 0 });
  const cameraTarget = useRef(new THREE.Vector3(0, 0, 0));
  
  // Animation targets
  const targetRotation = useRef({ phi: Math.PI / 2, theta: 0 });
  const targetZoom = useRef(targetPosition[2]);
  
  // Current spherical coordinates for smooth movement
  const currentSpherical = useRef(new THREE.Spherical(
    initialPosition[2], // radius
    Math.PI / 2,        // phi (vertical angle)
    0                   // theta (horizontal angle)
  ));

  // Auto transition state
  const transitionStartTime = useRef(0);
  const isTransitioning = useRef(false);

  // Initialize camera position
  useEffect(() => {
    camera.position.set(...initialPosition);
    camera.lookAt(cameraTarget.current);

    // Initialize spherical coordinates from initial position
    const initialOffset = new THREE.Vector3().subVectors(camera.position, cameraTarget.current);
    currentSpherical.current.setFromVector3(initialOffset);

    // Set initial rotation targets
    targetRotation.current = {
      phi: currentSpherical.current.phi,
      theta: currentSpherical.current.theta
    };

    targetZoom.current = currentSpherical.current.radius;

    // Start auto transition if enabled
    if (autoTransition) {
      setTimeout(() => {
        isTransitioning.current = true;
        transitionStartTime.current = performance.now();
        const targetOffset = new THREE.Vector3().subVectors(
          new THREE.Vector3(...targetPosition),
          cameraTarget.current
        );
        targetZoom.current = targetOffset.length();
      }, 100);
    }
  }, [camera, initialPosition, targetPosition, autoTransition]);

  // Animation loop
  useFrame(() => {
    const currentTime = performance.now();

    // Handle auto transition
    if (isTransitioning.current) {
      const elapsed = currentTime - transitionStartTime.current;
      const progress = Math.min(elapsed / transitionDuration, 1);
      
      const easedProgress = easeInOutCubic(progress);

      // Interpolate zoom
      const startZoom = initialPosition[2];
      const targetZoomDistance = targetPosition[2];
      currentSpherical.current.radius = startZoom + (targetZoomDistance - startZoom) * easedProgress;

      if (progress >= 1) {
        isTransitioning.current = false;
        targetZoom.current = targetZoomDistance;
      }
    } else {
      // Apply smooth zoom
      currentSpherical.current.radius += (targetZoom.current - currentSpherical.current.radius) * zoomEasing;
      currentSpherical.current.radius = clamp(currentSpherical.current.radius, minZoom, maxZoom);
    }

    if (!isDragging.current && mouseMoveEnabled) {
      // Mouse follow effect when not dragging
      const offset = new THREE.Vector3().subVectors(camera.position, cameraTarget.current);
      const followSpherical = new THREE.Spherical().setFromVector3(offset);
      
      followSpherical.radius = currentSpherical.current.radius;
      
      const targetFollowPhi = followSpherical.phi - mousePosition.current.y * mouseMoveStrength * 0.1;
      const targetFollowTheta = followSpherical.theta + mousePosition.current.x * mouseMoveStrength * 0.1;
      
      followSpherical.phi += (targetFollowPhi - followSpherical.phi) * mouseFollowEasing;
      followSpherical.theta += (targetFollowTheta - followSpherical.theta) * mouseFollowEasing;
      
      followSpherical.phi = clamp(followSpherical.phi, 0.01, Math.PI - 0.01);
      
      offset.setFromSpherical(followSpherical);
      camera.position.copy(cameraTarget.current).add(offset);
      camera.lookAt(cameraTarget.current);
      
      // Sync spherical coordinates
      currentSpherical.current.phi = followSpherical.phi;
      currentSpherical.current.theta = followSpherical.theta;
      targetRotation.current.phi = followSpherical.phi;
      targetRotation.current.theta = followSpherical.theta;

    } else if (isDragging.current && dragEnabled) {
      // Dragging mode
      currentSpherical.current.phi += (targetRotation.current.phi - currentSpherical.current.phi) * dragEasing;
      currentSpherical.current.theta += (targetRotation.current.theta - currentSpherical.current.theta) * dragEasing;
      
      currentSpherical.current.phi = clamp(currentSpherical.current.phi, 0.01, Math.PI - 0.01);
      
      const newPosition = new THREE.Vector3().setFromSpherical(currentSpherical.current);
      camera.position.copy(cameraTarget.current).add(newPosition);
      camera.lookAt(cameraTarget.current);
    }
  });

  // Mouse event handlers
  useEffect(() => {
    const canvas = gl.domElement;

    const handleMouseMove = (event) => {
      mousePosition.current = getMouseNDC(event.clientX, event.clientY);

      if (isDragging.current && dragEnabled) {
        const deltaX = event.clientX - previousMousePosition.current.x;
        const deltaY = event.clientY - previousMousePosition.current.y;

        previousMousePosition.current = {
          x: event.clientX,
          y: event.clientY
        };

        targetRotation.current.phi -= deltaY * dragSensitivity;
        targetRotation.current.theta -= deltaX * dragSensitivity;
        targetRotation.current.phi = clamp(targetRotation.current.phi, 0.01, Math.PI - 0.01);
      }
    };

    const handleMouseDown = (event) => {
      if (!dragEnabled) return;
      
      isDragging.current = true;
      previousMousePosition.current = {
        x: event.clientX,
        y: event.clientY
      };

      const offset = new THREE.Vector3().subVectors(camera.position, cameraTarget.current);
      const spherical = new THREE.Spherical().setFromVector3(offset);

      targetRotation.current.phi = spherical.phi;
      targetRotation.current.theta = spherical.theta;
      currentSpherical.current.copy(spherical);
    };

    const handleMouseUp = () => {
      isDragging.current = false;
    };

    canvas.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [gl, camera, dragEnabled, dragSensitivity]);

  return null;
};

export default CameraController; 