import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Navbar from './components/Navbar';
import GlobalPreloader from './components/GlobalPreloader';
import Unified from './pages/Unified';
// import Home from './pages/Home';
// import Projects from './pages/Projects';

function App() {
  return (
    <Router>
      <GlobalPreloader>
        <Navbar />
        <Suspense fallback={<div className="fixed inset-0 bg-black"></div>}>
          <Routes>
            <Route path="/" element={<Unified />} />
            {/* <Route path="/projects" element={<Projects />} /> */}
          </Routes>
        </Suspense>
      </GlobalPreloader>
    </Router>
  );
}

export default App;
