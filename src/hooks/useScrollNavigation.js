import { useRef } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useScroll } from '@react-three/drei';
import { useNavigate } from 'react-router-dom';
import * as THREE from 'three';

/**
 * Custom hook for handling scroll-based navigation
 * Manages smooth transitions between pages with camera animations
 */
export const useScrollNavigation = ({
  targetPage = '/projects',
  initialCameraPosition = [0, 0, 20],
  animationDuration = 800,
  scrollThreshold = 0.1,
  enabled = true
}) => {
  const data = useScroll();
  const navigate = useNavigate();
  const { camera } = useThree();
  
  const hasNavigatedRef = useRef(false);
  const prevOffsetRef = useRef(0);
  const lastUpdateTimeRef = useRef(0);
  const isAnimatingRef = useRef(false);
  const animationStartTimeRef = useRef(0);

  const handleScrollNavigation = () => {
    if (!enabled) return;

    useFrame(() => {
      const scrollProgress = data.offset;
      const currentTime = performance.now();
      
      // Detect scroll direction
      const scrollDelta = scrollProgress - prevOffsetRef.current;
      
      // Check if scrolling back to previous page (upward scroll near top)
      if (scrollDelta < -0.01 && scrollProgress < scrollThreshold && !isAnimatingRef.current) {
        isAnimatingRef.current = true;
        animationStartTimeRef.current = currentTime;
        console.log(`Starting navigation animation back from ${targetPage}`);
      }
      
      // Handle animation back to previous page
      if (isAnimatingRef.current && !hasNavigatedRef.current) {
        const elapsed = currentTime - animationStartTimeRef.current;
        const progress = Math.min(elapsed / animationDuration, 1);
        
        // Easing function for smooth animation
        const easeInOutCubic = (t) =>
          t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        const easedProgress = easeInOutCubic(progress);
        
        // Animate camera position
        const startPosition = new THREE.Vector3().copy(camera.position);
        const targetPosition = new THREE.Vector3(...initialCameraPosition);
        
        const newPosition = new THREE.Vector3().lerpVectors(
          startPosition,
          targetPosition,
          easedProgress
        );
        
        camera.position.copy(newPosition);
        
        // Complete animation and navigate
        if (progress >= 1 && !hasNavigatedRef.current) {
          hasNavigatedRef.current = true;
          setTimeout(() => {
            navigate(-1); // Go back to previous page
          }, 100);
        }
      }
      
      // Throttle updates
      if (currentTime - lastUpdateTimeRef.current > 16) {
        lastUpdateTimeRef.current = currentTime;
        prevOffsetRef.current = scrollProgress;
      }
    });
  };

  // For forward navigation (scroll down to next page)
  const handleForwardNavigation = (scrollProgress, onNavigate) => {
    if (!enabled) return false;

    // Navigate when scroll reaches near the end
    if (scrollProgress > 0.9 && !hasNavigatedRef.current) {
      hasNavigatedRef.current = true;
      setTimeout(() => {
        if (onNavigate) {
          onNavigate();
        } else {
          navigate(targetPage);
        }
      }, 100);
      return true;
    }
    return false;
  };

  return {
    handleScrollNavigation,
    handleForwardNavigation,
    scrollProgress: data?.offset || 0,
    isAnimating: isAnimatingRef.current,
    hasNavigated: hasNavigatedRef.current
  };
};

export default useScrollNavigation; 