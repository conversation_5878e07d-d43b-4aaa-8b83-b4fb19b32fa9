import React from 'react';
import { useScrollNavigation } from '../../hooks/useScrollNavigation';
import {
  CAMERA_INITIAL_POSITION
} from '../../constants/simpleParticleConstants';

/**
 * Projects Page Scroll Handler
 * Manages scroll-based navigation from Projects back to Home
 */
const ProjectsScrollHandler = () => {
  const { handleScrollNavigation } = useScrollNavigation({
    targetPage: '/',
    initialCameraPosition: CAMERA_INITIAL_POSITION,
    animationDuration: 800,
    scrollThreshold: 0.1, // Keep original threshold for upward scroll detection
    enabled: true
  });

  // Initialize the scroll handler
  handleScrollNavigation();

  return null;
};

export default ProjectsScrollHandler; 