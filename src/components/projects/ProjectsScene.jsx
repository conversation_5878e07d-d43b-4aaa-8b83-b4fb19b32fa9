import React, { useEffect } from 'react';
import { Suspense } from 'react';
import ParticleSystem from '../particles/ParticleSystem';
import CameraController from '../camera/CameraController';
import ProjectTile from './ProjectTile';
import TestTiles from './TestTiles';
// import TileScrollHandler from '../scroll/TileScrollHandler'; // Removed - now handled by UnifiedScrollHandler
import { useAssets } from '../../contexts/AssetContext';
import {
  BACKGROUND_COLOR,
  CAMERA_INITIAL_POSITION,
  CAMERA_POSITION,
  PARTICLE_COUNT,
  PARTICLE_SIZE,
  PARTICLE_DISTRIBUTION_RADIUS,
  PARTICLE_COLOR_1,
  PARTICLE_COLOR_2,
  MOUSE_MOVE_FACTOR,
  DRAG_SENSITIVITY,
  MAX_ZOOM_DISTANCE,
  MIN_ZOOM_DISTANCE,
  MOUSE_FOLLOW_EASING,
  DRAG_EASING,
  ZO<PERSON>_EASING,
  AMBIENT_LIGHT_COLOR,
  AMBIENT_LIGHT_INTENSITY,
  PORTFOLIO_ITEMS
} from '../../constants/simpleParticleConstants';

/**
 * Main Projects Scene Component
 * Orchestrates the galaxy background, camera controls, and project tiles
 */
const ProjectsScene = ({ disableCameraController = false, visible = true } = {}) => {
  const { assets, isLoaded } = useAssets();

  useEffect(() => {
    if (isLoaded && assets) {
      console.log('Assets loaded in Projects scene:', Object.keys(assets));
    }
  }, [isLoaded, assets]);

  return (
    <Suspense fallback={null}>
      <group visible={visible}>
        {/* Camera Controller with smooth transitions - disabled auto transition for tile control */}
        {!disableCameraController && (
          <CameraController
            initialPosition={CAMERA_INITIAL_POSITION}
            targetPosition={CAMERA_POSITION}
            mouseMoveEnabled={false}
            dragEnabled={false}
            mouseMoveStrength={MOUSE_MOVE_FACTOR}
            dragSensitivity={DRAG_SENSITIVITY}
            maxZoom={MAX_ZOOM_DISTANCE}
            minZoom={MIN_ZOOM_DISTANCE}
            mouseFollowEasing={MOUSE_FOLLOW_EASING}
            dragEasing={DRAG_EASING}
            zoomEasing={ZOOM_EASING}
            autoTransition={false}
            transitionDuration={1000}
          />
        )}

        {/* Tile Scroll Handler removed - now handled by UnifiedScrollHandler */}

        {/* Ambient lighting */}
        <ambientLight color={AMBIENT_LIGHT_COLOR} intensity={AMBIENT_LIGHT_INTENSITY} />
        <ParticleSystem />
        
        {/* Test Tiles scattered in galaxy space */}
        <TestTiles />

      {/* Point light for better depth perception */}
      {/* <pointLight position={[10, 10, 10]} intensity={0.5} color="#ffffff" /> */}

      {/* Galaxy Particle System Background */}

      {/* Project Tiles floating in space */}
      {/* {projects.map((project) => (
        <ProjectTile
          key={project.id}
          project={project}
          position={project.position}
          scale={1}
          onHover={handleProjectHover}
          onClick={handleProjectClick}
        />
      ))}

      {hoveredProject && (
        <group>
          <pointLight
            position={hoveredProject.position}
            intensity={0.3}
            color="#4f46e5"
            distance={10}
            decay={2}
          />
        </group>
      )}
        </Suspense> */}
      </group>
    </Suspense>
  );
};

// Scene setup component
// const ProjectsScene = () => {
//   const { assets, isLoaded } = useAssets();

//   useEffect(() => {
//       if (isLoaded && assets) {
//           console.log('Preloaded assets available in Projects scene:', Object.keys(assets));
//       }
//   }, [isLoaded, assets]);

//   return (
//       <>
//           {/* Camera controller */}
//           <CameraController />

//           {/* Lighting */}
//           <ambientLight color={AMBIENT_LIGHT_COLOR} intensity={AMBIENT_LIGHT_INTENSITY} />

//           {/* Particle system */}
//           <ParticleSystem />

//           {/* Portfolio items - will be implemented in future updates */}
//           {/* We can use the preloaded portfolio images from assets here */}
//       </>
//   );
// };

export default ProjectsScene; 