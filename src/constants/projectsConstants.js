/**
 * Projects Page Configuration Constants
 * Centralized configuration for project tiles and interactions
 */

// Project tile animation settings
export const TILE_ANIMATION = {
  FLOATING_SPEED: 0.5,
  FLOATING_AMPLITUDE: 0.1,
  ROTATION_SPEED: 0.3,
  ROTATION_AMPLITUDE: 0.1,
  HOVER_SCALE: 1.1,
  SCALE_EASING: 0.1
};

// Project tile appearance
export const TILE_APPEARANCE = {
  DEFAULT_COLOR: '#1e1b4b',
  HOVER_COLOR: '#4f46e5',
  EMISSIVE_COLOR: '#312e81',
  OPACITY: 0.8,
  SIZE: [2, 1.5, 0.1],
  TEXT_COLOR: 'white',
  DESCRIPTION_COLOR: '#94a3b8'
};

// Project tile lighting
export const TILE_LIGHTING = {
  HOVER_LIGHT_COLOR: '#4f46e5',
  HOVER_LIGHT_INTENSITY: 0.3,
  HOVER_LIGHT_DISTANCE: 10,
  HOVER_LIGHT_DECAY: 2
};

// Project navigation
export const PROJECT_NAVIGATION = {
  CLICK_DELAY: 100,
  TRANSITION_DURATION: 500
};

// Future project data structure template
export const PROJECT_TEMPLATE = {
  id: 0,
  title: "Project Title",
  description: "Project description",
  image: "/portfolio/project.png",
  link: "https://example.com",
  position: [0, 0, 0],
  tags: ["tag1", "tag2"],
  featured: false,
  status: "completed" // completed, in-progress, planned
};

export const TILE_POSITIONS = [
  { 
    id: 1, 
    position: [-25, 15, -35], 
    rotation: [0.3, 0.8, 0.2], 
    name: "Tile 1" 
  },
  { 
    id: 2, 
    position: [30, -20, -50], 
    rotation: [-0.4, -1.2, 0.6], 
    name: "Tile 2" 
  },
  { 
    id: 3, 
    position: [10, 30, -25], 
    rotation: [0.7, 0.5, -0.9], 
    name: "Tile 3" 
  },
  { 
    id: 4, 
    position: [-40, -25, -65], 
    rotation: [-0.6, 1.4, 0.8], 
    name: "Tile 4" 
  },
  { 
    id: 5, 
    position: [45, 12, -30], 
    rotation: [0.9, -0.7, -1.1], 
    name: "Tile 5" 
  }
];

export default {
  TILE_ANIMATION,
  TILE_APPEARANCE,
  TILE_LIGHTING,
  PROJECT_NAVIGATION,
  PROJECT_TEMPLATE,
  TILE_POSITIONS
}; 