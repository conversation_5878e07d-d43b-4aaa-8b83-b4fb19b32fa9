/**
 * Constants for the ParticleText component
 */

// Text constants
export const DEFAULT_TEXT = "CHERYL TOH";
export const MOBILE_TEXT = "CHERYL\nTOH";
export const MOBILE_BREAKPOINT = 768; // Width in pixels below which we use mobile layout

// Particle system constants
export const PARTICLE_AMOUNT = 3000;
export const PARTICLE_SIZE = 1;
export const PARTICLE_COLOR = 0xffffff; // White
export const INTERACTION_AREA = 400;
export const DEFAULT_EASE = 0.07;
export const PRESSED_EASE = 0.01;
export const RELEASED_EASE = 0.05;

// Image constants
export const DEFAULT_IMAGE_URL = "/homepageme.png";
export const DESKTOP_IMAGE_SIZE = 70;
export const MOBILE_IMAGE_SIZE = 56;

// Text size calculation
export const TEXT_SIZE_DIVIDER = 80; // Divides window width to get text size
export const MIN_TEXT_SIZE = 10;

// Text positioning
export const DESKTOP_TEXT_Y_POSITION = 0;
export const MOBILE_TEXT_Y_POSITION = 30;


// Asset URLs
export const FONT_URL = "https://res.cloudinary.com/dydre7amr/raw/upload/v1612950355/font_zsd4dr.json";
export const PARTICLE_IMAGE_URL = "https://res.cloudinary.com/dfvtkoboz/image/upload/v1605013866/particle_a64uzf.png";

// Animation constants
export const IMAGE_ANIMATION_DURATION = 1500;
export const IMAGE_START_Y = -30;
export const DESKTOP_IMAGE_TARGET_Y = 0;
export const MOBILE_IMAGE_TARGET_Y = 5;
export const IMAGE_Z_POSITION = 10;

// Mouse interaction constants
export const MOUSE_MOVE_FACTOR = 0.03;
export const MOUSE_ROTATION_FACTOR = 0.2;
export const ROTATION_EASE = 0.05;
export const PARTICLE_DISTANCE_THRESHOLD = {
  CLOSE: 10,
  FAR: 70
};

// Material constants
export const MATERIAL_SETTINGS = {
  ROUGHNESS: 0.1,
  METALNESS: 0.0,
  COLOR: 0xffffff,
  EMISSIVE: 0x111111,
  EMISSIVE_INTENSITY: 0.8
};

// Light settings
export const AMBIENT_LIGHT_INTENSITY = 1.2;
export const DIRECTIONAL_LIGHT_INTENSITY = 0.5;
export const DIRECTIONAL_LIGHT_POSITION = [1, 1, 1];

// Camera settings
export const CAMERA_FOV = 65;
export const CAMERA_POSITION = [0, 0, 100];
export const CAMERA_NEAR = 1;
export const CAMERA_FAR = 10000;

// Misc
export const RESIZE_DEBOUNCE_TIME = 100;
export const FONT_RETRY_DELAY = 2000;
