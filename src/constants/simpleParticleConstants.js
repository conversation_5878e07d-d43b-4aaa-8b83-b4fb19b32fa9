/**
 * Constants for the simple particle scene
 */

// Scene settings
export const BACKGROUND_COLOR = 0x000000;
export const FOG_COLOR = 0x000000;
export const FOG_NEAR = 10;
export const FOG_FAR = 100;

// Camera settings
export const CAMERA_FOV = 75;
export const CAMERA_NEAR = 0.1;
export const CAMERA_FAR = 1000;
export const CAMERA_INITIAL_POSITION = [0, 0, 20];
export const CAMERA_POSITION = [0, 0, 25];

// Particle settings
export const PARTICLE_COUNT = 8000; // Increased for more stars
export const PARTICLE_SIZE = 0.1; // Larger size for visibility
export const PARTICLE_DISTRIBUTION_RADIUS = 100; // Larger distribution for galaxy effect
export const PARTICLE_SPEED = 0; // Static particles (no movement)
export const PARTICLE_COLOR_1 = 0xffffff; // White
export const PARTICLE_COLOR_2 = 0xffffff; // Also white for consistent star color

// Camera and interaction settings
export const MOUSE_MOVE_FACTOR = 0.1;
export const DRAG_SENSITIVITY = 0.01;
export const ZOOM_SENSITIVITY = 1.0;
export const MAX_ZOOM_DISTANCE = 150;
export const MIN_ZOOM_DISTANCE = 20;

// Easing settings
export const MOUSE_FOLLOW_EASING = 0.03; // Lower = smoother/slower
export const DRAG_EASING = 0.08; // Easing for drag rotation
export const ZOOM_EASING = 0.1; // Easing for zoom

// Lighting
export const AMBIENT_LIGHT_COLOR = 0x404040;
export const AMBIENT_LIGHT_INTENSITY = 1.0;

// Portfolio items
// Portfolio items data
export const PORTFOLIO_ITEMS = [
  {
    id: 1,
    title: "Project 1",
    description: "A brief description of project 1",
    image: "/portfolio/project1.png",
    link: "https://example.com/project1",
    position: [-20, 10, -30]
  },
  {
    id: 2,
    title: "Project 2",
    description: "A brief description of project 2",
    image: "/portfolio/project2.png",
    link: "https://example.com/project2",
    position: [0, 0, -40]
  },
  {
    id: 3,
    title: "Project 3",
    description: "A brief description of project 3",
    image: "/portfolio/project3.png",
    link: "https://example.com/project3",
    position: [20, -10, -35]
  },
  {
    id: 4,
    title: "Project 4",
    description: "A brief description of project 4",
    image: "/portfolio/project4.png",
    link: "https://example.com/project4",
    position: [-15, -15, -25]
  }
];
